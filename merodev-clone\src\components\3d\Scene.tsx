'use client';

import { Canvas } from '@react-three/fiber';
import { Suspense } from 'react';
import { SCENE_CONFIG, CAMERA_CONFIG } from '@/utils/constants';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { PerformanceMonitor } from '@react-three/drei';
import { Perf } from 'r3f-perf';

interface SceneProps {
  children: React.ReactNode;
  showPerformanceMonitor?: boolean;
}

export function Scene({ children, showPerformanceMonitor = false }: SceneProps) {
  return (
    <div className="w-full h-screen relative">
      <Canvas
        camera={{
          position: [CAMERA_CONFIG.position.x, CAMERA_CONFIG.position.y, CAMERA_CONFIG.position.z],
          fov: CAMERA_CONFIG.fov,
          near: CAMERA_CONFIG.near,
          far: CAMERA_CONFIG.far,
        }}
        shadows={SCENE_CONFIG.shadows.enabled}
        gl={{
          antialias: true,
          alpha: false,
          powerPreference: 'high-performance',
          stencil: false,
          depth: true,
        }}
        dpr={[1, 2]}
        performance={{
          min: 0.5,
          max: 1,
          debounce: 200,
        }}
      >
        {/* Performance monitoring */}
        {showPerformanceMonitor && (
          <>
            <Perf position="top-left" />
            <PerformanceMonitor
              onIncline={() => console.log('Performance improved')}
              onDecline={() => console.log('Performance declined')}
            />
          </>
        )}

        {/* Scene background */}
        <color attach="background" args={[SCENE_CONFIG.background]} />
        
        {/* Fog for depth */}
        <fog
          attach="fog"
          args={[SCENE_CONFIG.fog.color, SCENE_CONFIG.fog.near, SCENE_CONFIG.fog.far]}
        />

        {/* Suspense boundary for async loading */}
        <Suspense fallback={null}>
          {children}
        </Suspense>
      </Canvas>

      {/* Loading overlay */}
      <Suspense fallback={<LoadingScreen />}>
        <div />
      </Suspense>
    </div>
  );
}
