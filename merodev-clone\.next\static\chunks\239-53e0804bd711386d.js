"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[239],{620:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],p=1,d=null,h=3,m=!1,b=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function E(e){if(v=!1,j(e),!b)if(null!==r(u))b=!0,S();else{var t=r(f);null!==t&&A(E,t.startTime-e)}}var P=!1,x=-1,C=5,O=-1;function M(){return!(t.unstable_now()-O<C)}function k(){if(P){var e=t.unstable_now();O=e;var n=!0;try{e:{b=!1,v&&(v=!1,y(x),x=-1),m=!0;var i=h;try{t:{for(j(e),d=r(u);null!==d&&!(d.expirationTime>e&&M());){var l=d.callback;if("function"==typeof l){d.callback=null,h=d.priorityLevel;var s=l(d.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){d.callback=s,j(e),n=!0;break t}d===r(u)&&o(u),j(e)}else o(u);d=r(u)}if(null!==d)n=!0;else{var c=r(f);null!==c&&A(E,c.startTime-e),n=!1}}break e}finally{d=null,h=i,m=!1}}}finally{n?a():P=!1}}}if("function"==typeof w)a=function(){w(k)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,_=T.port2;T.port1.onmessage=k,a=function(){_.postMessage(null)}}else a=function(){g(k,0)};function S(){P||(P=!0,a())}function A(e,n){x=g(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||m||(b=!0,S())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:p++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(v?(y(x),x=-1):v=!0,A(E,i-a))):(e.sortIndex=l,n(u,e),b||m||(b=!0,S())),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},1933:(e,t,n)=>{e.exports=n(6500)},2436:(e,t,n)=>{var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,u=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&u({inst:o})},[e,n,t]),a(function(){return c(o)&&u({inst:o}),e(function(){c(o)&&u({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},2932:(e,t,n)=>{let r,o,i,a,l;n.d(t,{A:()=>eu,B:()=>H,C:()=>ef,E:()=>Y,a:()=>D,b:()=>z,c:()=>ez,d:()=>eN,e:()=>ej,f:()=>eG,i:()=>L,u:()=>N});var s=n(3264),c=n(7431),u=n(2115),f=n.t(u,2),p=n(1933),d=n(5643);let h=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},m=e=>e?h(e):h,{useSyncExternalStoreWithSelector:b}=d,v=e=>e,g=(e,t)=>{let n=m(e),r=(e,r=t)=>(function(e,t=v,n){let r=b(e.subscribe,e.getState,e.getInitialState,t,n);return u.useDebugValue(r),r})(n,e,r);return Object.assign(r,n),r},y=(e,t)=>e?g(e,t):g;var w=n(5220),j=n.n(w),E=n(4342);let P=e=>"object"==typeof e&&"function"==typeof e.then,x=[];function C(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let o=0;o<r;o++)if(!n(e[o],t[o]))return!1;return!0}function O(e,t=null,n=!1,r={}){for(let o of(null===t&&(t=[e]),x))if(C(t,o.keys,o.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(o,"error"))throw o.error;if(Object.prototype.hasOwnProperty.call(o,"response"))return r.lifespan&&r.lifespan>0&&(o.timeout&&clearTimeout(o.timeout),o.timeout=setTimeout(o.remove,r.lifespan)),o.response;if(!n)throw o.promise}let o={keys:t,equal:r.equal,remove:()=>{let e=x.indexOf(o);-1!==e&&x.splice(e,1)},promise:(P(e)?e:e(...t)).then(e=>{o.response=e,r.lifespan&&r.lifespan>0&&(o.timeout=setTimeout(o.remove,r.lifespan))}).catch(e=>o.error=e)};if(x.push(o),!n)throw o.promise}let M=(e,t,n)=>O(e,t,!1,n),k=(e,t,n)=>void O(e,t,!0,n),T=e=>{if(void 0===e||0===e.length)x.splice(0,x.length);else{let t=x.find(t=>C(e,t.keys,t.equal));t&&t.remove()}};var _=n(5155),S=n(6354);function A(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}n(9509),f.act;let I=e=>e&&e.isOrthographicCamera,L=e=>e&&e.hasOwnProperty("current"),R=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),z=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?u.useLayoutEffect:u.useEffect;function D(e){let t=u.useRef(e);return z(()=>void(t.current=e),[e]),t}function N(){let e=(0,S.u5)(),t=(0,S.y3)();return u.useMemo(()=>({children:n})=>{let r=(0,S.Nz)(e,!0,e=>e.type===u.StrictMode)?u.StrictMode:u.Fragment;return(0,_.jsx)(r,{children:(0,_.jsx)(t,{children:n})})},[e,t])}function H({set:e}){return z(()=>(e(new Promise(()=>null)),()=>e(!1)),[e]),null}let Y=(e=>((e=class extends u.Component{constructor(...e){super(...e),this.state={error:!1}}componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}}).getDerivedStateFromError=()=>({error:!0}),e))();function F(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}function q(e){var t;return null==(t=e.__r3f)?void 0:t.root.getState()}let U={obj:e=>e===Object(e)&&!U.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:n="shallow",objects:r="reference",strict:o=!0}={}){let i;if(typeof e!=typeof t||!!e!=!!t)return!1;if(U.str(e)||U.num(e)||U.boo(e))return e===t;let a=U.obj(e);if(a&&"reference"===r)return e===t;let l=U.arr(e);if(l&&"reference"===n)return e===t;if((l||a)&&e===t)return!0;for(i in e)if(!(i in t))return!1;if(a&&"shallow"===n&&"shallow"===r){for(i in o?t:e)if(!U.equ(e[i],t[i],{strict:o,objects:"reference"}))return!1}else for(i in o?t:e)if(e[i]!==t[i])return!1;if(U.und(i)){if(l&&0===e.length&&0===t.length||a&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}},B=["children","key","ref"];function V(e,t,n,r){let o=null==e?void 0:e.__r3f;return!o&&(o={root:t,type:n,parent:null,children:[],props:function(e){let t={};for(let n in e)B.includes(n)||(t[n]=e[n]);return t}(r),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=o)),o}function X(e,t){let n=e[t];if(!t.includes("-"))return{root:e,key:t,target:n};for(let o of(n=e,t.split("-"))){var r;t=o,e=n,n=null==(r=n)?void 0:r[t]}return{root:e,key:t,target:n}}let Z=/-\d+$/;function W(e,t){if(U.str(t.props.attach)){if(Z.test(t.props.attach)){let n=t.props.attach.replace(Z,""),{root:r,key:o}=X(e.object,n);Array.isArray(r[o])||(r[o]=[])}let{root:n,key:r}=X(e.object,t.props.attach);t.previousAttach=n[r],n[r]=t.object}else U.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function K(e,t){if(U.str(t.props.attach)){let{root:n,key:r}=X(e.object,t.props.attach),o=t.previousAttach;void 0===o?delete n[r]:n[r]=o}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let $=[...B,"args","dispose","attach","object","onUpdate","dispose"],G=new Map,Q=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],J=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function ee(e,t){var n,r;let o=e.__r3f,i=o&&A(o).getState(),a=null==o?void 0:o.eventCount;for(let n in t){let a=t[n];if($.includes(n))continue;if(o&&J.test(n)){"function"==typeof a?o.handlers[n]=a:delete o.handlers[n],o.eventCount=Object.keys(o.handlers).length;continue}if(void 0===a)continue;let{root:l,key:c,target:u}=X(e,n);u instanceof s.zgK&&a instanceof s.zgK?u.mask=a.mask:u instanceof s.Q1f&&R(a)?u.set(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"function"==typeof u.copy&&null!=a&&a.constructor&&u.constructor===a.constructor?u.copy(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&Array.isArray(a)?"function"==typeof u.fromArray?u.fromArray(a):u.set(...a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"number"==typeof a?"function"==typeof u.setScalar?u.setScalar(a):u.set(a):(l[c]=a,i&&!i.linear&&Q.includes(c)&&null!=(r=l[c])&&r.isTexture&&l[c].format===s.GWd&&l[c].type===s.OUM&&(l[c].colorSpace=s.er$))}if(null!=o&&o.parent&&null!=i&&i.internal&&null!=(n=o.object)&&n.isObject3D&&a!==o.eventCount){let e=o.object,t=i.internal.interaction.indexOf(e);t>-1&&i.internal.interaction.splice(t,1),o.eventCount&&null!==e.raycast&&i.internal.interaction.push(e)}return o&&void 0===o.props.attach&&(o.object.isBufferGeometry?o.props.attach="geometry":o.object.isMaterial&&(o.props.attach="material")),o&&et(o),e}function et(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let n=null==(t=e.root)||null==t.getState?void 0:t.getState();n&&0===n.internal.frames&&n.invalidate()}function en(e,t){e.manual||(I(e)?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}let er=e=>null==e?void 0:e.isObject3D;function eo(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function ei(e,t,n,r){let o=n.get(t);o&&(n.delete(t),0===n.size&&(e.delete(r),o.target.releasePointerCapture(r)))}let ea=e=>!!(null!=e&&e.render),el=u.createContext(null),es=(e,t)=>{let n=y((n,r)=>{let o,i=new s.Pq0,a=new s.Pq0,l=new s.Pq0;function c(e=r().camera,t=a,n=r().size){let{width:o,height:s,top:u,left:f}=n,p=o/s;t.isVector3?l.copy(t):l.set(...t);let d=e.getWorldPosition(i).distanceTo(l);if(I(e))return{width:o/e.zoom,height:s/e.zoom,top:u,left:f,factor:1,distance:d,aspect:p};{let t=2*Math.tan(e.fov*Math.PI/180/2)*d,n=o/s*t;return{width:n,height:t,top:u,left:f,factor:o/n,distance:d,aspect:p}}}let f=e=>n(t=>({performance:{...t.performance,current:e}})),p=new s.I9Y;return{set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:(t=1)=>e(r(),t),advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new s.zD7,pointer:p,mouse:p,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();o&&clearTimeout(o),e.performance.current!==e.performance.min&&f(e.performance.min),o=setTimeout(()=>f(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:c},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,o=0,i=0)=>{let l=r().camera,s={width:e,height:t,top:o,left:i};n(e=>({size:s,viewport:{...e.viewport,...c(l,a,s)}}))},setDpr:e=>n(t=>{let n=F(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:(e="always")=>{let t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:u.createRef(),active:!1,frames:0,priority:0,subscribe:(e,t,n)=>{let o=r().internal;return o.priority=o.priority+ +(t>0),o.subscribers.push({ref:e,priority:t,store:n}),o.subscribers=o.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}}}),r=n.getState(),o=r.size,i=r.viewport.dpr,a=r.camera;return n.subscribe(()=>{let{camera:e,size:t,viewport:r,gl:l,set:s}=n.getState();if(t.width!==o.width||t.height!==o.height||r.dpr!==i){o=t,i=r.dpr,en(e,t),r.dpr>0&&l.setPixelRatio(r.dpr);let n="undefined"!=typeof HTMLCanvasElement&&l.domElement instanceof HTMLCanvasElement;l.setSize(t.width,t.height,n)}e!==a&&(a=e,s(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),n.subscribe(t=>e(t)),n};function ec(){let e=u.useContext(el);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function eu(e=e=>e,t){return ec()(e,t)}function ef(e,t=0){let n=ec(),r=n.getState().internal.subscribe,o=D(e);return z(()=>r(o,t,n),[t,r,n]),null}let ep=new WeakMap,ed=e=>{var t;return"function"==typeof e&&(null==e||null==(t=e.prototype)?void 0:t.constructor)===e};function eh(e,t){return function(n,...r){let o;return ed(n)?(o=ep.get(n))||(o=new n,ep.set(n,o)):o=n,e&&e(o),Promise.all(r.map(e=>new Promise((n,r)=>o.load(e,e=>{er(null==e?void 0:e.scene)&&Object.assign(e,function(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}(e.scene)),n(e)},t,t=>r(Error(`Could not load ${e}: ${null==t?void 0:t.message}`))))))}}function em(e,t,n,r){let o=Array.isArray(t)?t:[t],i=M(eh(n,r),[e,...o],{equal:U.equ});return Array.isArray(t)?i:i[0]}em.preload=function(e,t,n){let r=Array.isArray(t)?t:[t];return k(eh(n),[e,...r])},em.clear=function(e,t){return T([e,...Array.isArray(t)?t:[t]])};let eb={},ev=/^three(?=[A-Z])/,eg=e=>`${e[0].toUpperCase()}${e.slice(1)}`,ey=0,ew=e=>"function"==typeof e;function ej(e){if(ew(e)){let t=`${ey++}`;return eb[t]=e,t}Object.assign(eb,e)}function eE(e,t){let n=eg(e),r=eb[n];if("primitive"!==e&&!r)throw Error(`R3F: ${n} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function eP(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?W(e.parent,e):er(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,et(e)}}function ex(e,t,n){let r=t.root.getState();if(e.parent||e.object===r.scene){if(!t.object){var o,i;let e=eb[eg(t.type)];t.object=null!=(o=t.props.object)?o:new e(...null!=(i=t.props.args)?i:[]),t.object.__r3f=t}if(ee(t.object,t.props),t.props.attach)W(e,t);else if(er(t.object)&&er(e.object)){let r=e.object.children.indexOf(null==n?void 0:n.object);if(n&&-1!==r){let n=e.object.children.indexOf(t.object);-1!==n?(e.object.children.splice(n,1),e.object.children.splice(n<r?r-1:r,0,t.object)):(t.object.parent=e.object,e.object.children.splice(r,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)ex(t,e);et(t)}}function eC(e,t){t&&(t.parent=e,e.children.push(t),ex(e,t))}function eO(e,t,n){if(!t||!n)return;t.parent=e;let r=e.children.indexOf(n);-1!==r?e.children.splice(r,0,t):e.children.push(t),ex(e,t,n)}function eM(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,E.unstable_scheduleCallback)(E.unstable_IdlePriority,t)}}function ek(e,t,n){if(!t)return;t.parent=null;let r=e.children.indexOf(t);-1!==r&&e.children.splice(r,1),t.props.attach?K(e,t):er(t.object)&&er(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{ei(n.capturedMap,t,e,r)})}(A(t),t.object));let o=null!==t.props.dispose&&!1!==n;for(let e=t.children.length-1;e>=0;e--){let n=t.children[e];ek(t,n,o)}t.children.length=0,delete t.object.__r3f,o&&"primitive"!==t.type&&"Scene"!==t.object.type&&eM(t.object),void 0===n&&et(t)}let eT=[],e_=()=>{},eS={},eA=0,eI=function(e){let t=j()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:u.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,n){var r;return eE(e=eg(e)in eb?e:e.replace(ev,""),t),"primitive"===e&&null!=(r=t.object)&&r.__r3f&&delete t.object.__r3f,V(t.object,n,e,t)},removeChild:ek,appendChild:eC,appendInitialChild:eC,insertBefore:eO,appendChildToContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eC(n,t)},removeChildFromContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&ek(n,t)},insertInContainerBefore(e,t,n){let r=e.getState().scene.__r3f;t&&n&&r&&eO(r,t,n)},getRootHostContext:()=>eS,getChildHostContext:()=>eS,commitUpdate(e,t,n,r,o){var i,a,l;eE(t,r);let s=!1;if("primitive"===e.type&&n.object!==r.object||(null==(i=r.args)?void 0:i.length)!==(null==(a=n.args)?void 0:a.length)?s=!0:null!=(l=r.args)&&l.some((e,t)=>{var r;return e!==(null==(r=n.args)?void 0:r[t])})&&(s=!0),s)eT.push([e,{...r},o]);else{let t=function(e,t){let n={};for(let r in t)if(!$.includes(r)&&!U.equ(t[r],e.props[r]))for(let e in n[r]=t[r],t)e.startsWith(`${r}-`)&&(n[e]=t[e]);for(let r in e.props){if($.includes(r)||t.hasOwnProperty(r))continue;let{root:o,key:i}=X(e.object,r);if(o.constructor&&0===o.constructor.length){let e=function(e){let t=G.get(e.constructor);try{t||(t=new e.constructor,G.set(e.constructor,t))}catch(e){}return t}(o);U.und(e)||(n[i]=e[i])}else n[i]=0}return n}(e,r);Object.keys(t).length&&(Object.assign(e.props,t),ee(e.object,t))}(null===o.sibling||(4&o.flags)==0)&&function(){for(let[e]of eT){let t=e.parent;if(t)for(let n of(e.props.attach?K(t,e):er(e.object)&&er(t.object)&&t.object.remove(e.object),e.children))n.props.attach?K(e,n):er(n.object)&&er(e.object)&&e.object.remove(n.object);e.isHidden&&eP(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&eM(e.object)}for(let[r,o,i]of eT){r.props=o;let a=r.parent;if(a){let o=eb[eg(r.type)];r.object=null!=(e=r.props.object)?e:new o(...null!=(t=r.props.args)?t:[]),r.object.__r3f=r;var e,t,n=r.object;for(let e of[i,i.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(n);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=n);for(let e of(ee(r.object,r.props),r.props.attach?W(a,r):er(r.object)&&er(a.object)&&a.object.add(r.object),r.children))e.props.attach?W(r,e):er(e.object)&&er(r.object)&&r.object.add(e.object);et(r)}}eT.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>V(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?K(e.parent,e):er(e.object)&&(e.object.visible=!1),e.isHidden=!0,et(e)}},unhideInstance:eP,createTextInstance:e_,hideTextInstance:e_,unhideTextInstance:e_,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:u.createContext(null),setCurrentUpdatePriority(e){eA=e},getCurrentUpdatePriority:()=>eA,resolveUpdatePriority(){var e;if(0!==eA)return eA;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return p.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return p.ContinuousEventPriority;default:return p.DefaultEventPriority}},resetFormInstance(){}}),eL=new Map,eR={objects:"shallow",strict:!1};function ez(e){let t,n,r=eL.get(e),o=null==r?void 0:r.fiber,i=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let a="function"==typeof reportError?reportError:console.error,l=i||es(eW,eK),u=o||eI.createContainer(l,p.ConcurrentRoot,null,!1,null,"",a,a,a,null);r||eL.set(e,{fiber:u,store:l});let f=!1,d=null;return{async configure(r={}){var o,i;let a;d=new Promise(e=>a=e);let{gl:u,size:p,scene:h,events:m,onCreated:b,shadows:v=!1,linear:g=!1,flat:y=!1,legacy:w=!1,orthographic:j=!1,frameloop:E="always",dpr:P=[1,2],performance:x,raycaster:C,camera:O,onPointerMissed:M}=r,k=l.getState(),T=k.gl;if(!k.gl){let t={canvas:e,powerPreference:"high-performance",antialias:!0,alpha:!0},n="function"==typeof u?await u(t):u;T=ea(n)?n:new c.WebGLRenderer({...t,...u}),k.set({gl:T})}let _=k.raycaster;_||k.set({raycaster:_=new s.tBo});let{params:S,...A}=C||{};if(U.equ(A,_,eR)||ee(_,{...A}),U.equ(S,_.params,eR)||ee(_,{params:{..._.params,...S}}),!k.camera||k.camera===n&&!U.equ(n,O,eR)){n=O;let e=null==O?void 0:O.isCamera,t=e?O:j?new s.qUd(0,0,0,0,.1,1e3):new s.ubm(75,0,.1,1e3);!e&&(t.position.z=5,O&&(ee(t,O),!t.manual&&("aspect"in O||"left"in O||"right"in O||"bottom"in O||"top"in O)&&(t.manual=!0,t.updateProjectionMatrix())),k.camera||null!=O&&O.rotation||t.lookAt(0,0,0)),k.set({camera:t}),_.camera=t}if(!k.scene){let e;null!=h&&h.isScene?V(e=h,l,"",{}):(V(e=new s.Z58,l,"",{}),h&&ee(e,h)),k.set({scene:e})}m&&!k.events.handlers&&k.set({events:m(l)});let I=function(e,t){if(!t&&"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:n,top:r,left:o}=e.parentElement.getBoundingClientRect();return{width:t,height:n,top:r,left:o}}return!t&&"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...t}}(e,p);if(U.equ(I,k.size,eR)||k.setSize(I.width,I.height,I.top,I.left),P&&k.viewport.dpr!==F(P)&&k.setDpr(P),k.frameloop!==E&&k.setFrameloop(E),k.onPointerMissed||k.set({onPointerMissed:M}),x&&!U.equ(x,k.performance,eR)&&k.set(e=>({performance:{...e.performance,...x}})),!k.xr){let e=(e,t)=>{let n=l.getState();"never"!==n.frameloop&&eK(e,!0,n,t)},t=()=>{let t=l.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||eW(t)},n={connect(){let e=l.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=l.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(o=T.xr)?void 0:o.addEventListener)&&n.connect(),k.set({xr:n})}if(T.shadowMap){let e=T.shadowMap.enabled,t=T.shadowMap.type;if(T.shadowMap.enabled=!!v,U.boo(v))T.shadowMap.type=s.Wk7;else if(U.str(v)){let e={basic:s.bTm,percentage:s.QP0,soft:s.Wk7,variance:s.RyA};T.shadowMap.type=null!=(i=e[v])?i:s.Wk7}else U.obj(v)&&Object.assign(T.shadowMap,v);(e!==T.shadowMap.enabled||t!==T.shadowMap.type)&&(T.shadowMap.needsUpdate=!0)}return s.ppV.enabled=!w,f||(T.outputColorSpace=g?s.Zr2:s.er$,T.toneMapping=y?s.y_p:s.FV),k.legacy!==w&&k.set(()=>({legacy:w})),k.linear!==g&&k.set(()=>({linear:g})),k.flat!==y&&k.set(()=>({flat:y})),!u||U.fun(u)||ea(u)||U.equ(u,T,eR)||ee(T,u),t=b,f=!0,a(),this},render(n){return f||d||this.configure(),d.then(()=>{eI.updateContainer((0,_.jsx)(eD,{store:l,children:n,onCreated:t,rootElement:e}),u,null,()=>void 0)}),l},unmount(){eN(e)}}}function eD({store:e,children:t,onCreated:n,rootElement:r}){return z(()=>{let t=e.getState();t.set(e=>({internal:{...e.internal,active:!0}})),n&&n(t),e.getState().events.connected||null==t.events.connect||t.events.connect(r)},[]),(0,_.jsx)(el.Provider,{value:e,children:t})}function eN(e,t){let n=eL.get(e),r=null==n?void 0:n.fiber;if(r){let o=null==n?void 0:n.store.getState();o&&(o.internal.active=!1),eI.updateContainer(null,r,null,()=>{o&&setTimeout(()=>{try{null==o.events.disconnect||o.events.disconnect(),null==(n=o.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(i=o.gl)||null==i.forceContextLoss||i.forceContextLoss(),null!=(a=o.gl)&&a.xr&&o.xr.disconnect();var n,r,i,a,l=o.scene;for(let e in"Scene"!==l.type&&(null==l.dispose||l.dispose()),l){let t=l[e];(null==t?void 0:t.type)!=="Scene"&&(null==t||null==t.dispose||t.dispose())}eL.delete(e),t&&t(e)}catch(e){}},500)})}}let eH=new Set,eY=new Set,eF=new Set;function eq(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function eU(e,t){switch(e){case"before":return eq(eH,t);case"after":return eq(eY,t);case"tail":return eq(eF,t)}}function eB(e,t,n){let i=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(i=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),r=t.internal.subscribers;for(let e=0;e<r.length;e++)(o=r[e]).ref.current(o.store.getState(),i,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let eV=!1,eX=!1;function eZ(e){for(let n of(a=requestAnimationFrame(eZ),eV=!0,i=0,eU("before",e),eX=!0,eL.values())){var t;(l=n.store.getState()).internal.active&&("always"===l.frameloop||l.internal.frames>0)&&!(null!=(t=l.gl.xr)&&t.isPresenting)&&(i+=eB(e,l))}if(eX=!1,eU("after",e),0===i)return eU("tail",e),eV=!1,cancelAnimationFrame(a)}function eW(e,t=1){var n;if(!e)return eL.forEach(e=>eW(e.store.getState(),t));(null==(n=e.gl.xr)||!n.isPresenting)&&e.internal.active&&"never"!==e.frameloop&&(t>1?e.internal.frames=Math.min(60,e.internal.frames+t):eX?e.internal.frames=2:e.internal.frames=1,eV||(eV=!0,requestAnimationFrame(eZ)))}function eK(e,t=!0,n,r){if(t&&eU("before",e),n)eB(e,n,r);else for(let t of eL.values())eB(e,t.store.getState());t&&eU("after",e)}let e$={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eG(e){let{handlePointer:t}=function(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject.__r3f;if(n.hovered.delete(eo(e)),null!=r&&r.eventCount){let n=r.handlers,o={...e,intersections:t};null==n.onPointerOut||n.onPointerOut(o),null==n.onPointerLeave||n.onPointerLeave(o)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(o){switch(o){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(i){let{onPointerMissed:a,internal:l}=e.getState();l.lastEvent.current=i;let c="onPointerMove"===o,u="onClick"===o||"onContextMenu"===o||"onDoubleClick"===o,f=function(t,n){let r=e.getState(),o=new Set,i=[],a=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<a.length;e++){let t=q(a[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let l=a.flatMap(function(e){let n=q(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=q(e.object),r=q(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=eo(e);return!o.has(t)&&(o.add(t),!0)});for(let e of(r.events.filter&&(l=r.events.filter(l,r)),l)){let t=e.object;for(;t;){var s;null!=(s=t.__r3f)&&s.eventCount&&i.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())o.has(eo(e.intersection))||i.push(e.intersection);return i}(i,c?t:void 0),p=u?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],o=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+o*o))}(i):0;"onPointerDown"===o&&(l.initialClick=[i.offsetX,i.offsetY],l.initialHits=f.map(e=>e.eventObject)),u&&!f.length&&p<=2&&(r(i,l.interaction),a&&a(i)),c&&n(f),!function(e,t,r,o){if(e.length){let i={stopped:!1};for(let a of e){let l=q(a.object);if(l||a.object.traverseAncestors(e=>{let t=q(e);if(t)return l=t,!1}),l){let{raycaster:c,pointer:u,camera:f,internal:p}=l,d=new s.Pq0(u.x,u.y,0).unproject(f),h=e=>{var t,n;return null!=(t=null==(n=p.capturedMap.get(e))?void 0:n.has(a.eventObject))&&t},m=e=>{let n={intersection:a,target:t.target};p.capturedMap.has(e)?p.capturedMap.get(e).set(a.eventObject,n):p.capturedMap.set(e,new Map([[a.eventObject,n]])),t.target.setPointerCapture(e)},b=e=>{let t=p.capturedMap.get(e);t&&ei(p.capturedMap,a.eventObject,t,e)},v={};for(let e in t){let n=t[e];"function"!=typeof n&&(v[e]=n)}let g={...a,...v,pointer:u,intersections:e,stopped:i.stopped,delta:r,unprojectedPoint:d,ray:c.ray,camera:f,stopPropagation(){let r="pointerId"in t&&p.capturedMap.get(t.pointerId);(!r||r.has(a.eventObject))&&(g.stopped=i.stopped=!0,p.hovered.size&&Array.from(p.hovered.values()).find(e=>e.eventObject===a.eventObject)&&n([...e.slice(0,e.indexOf(a)),a]))},target:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:b},currentTarget:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:b},nativeEvent:t};if(o(g),!0===i.stopped)break}}}}(f,i,p,function(e){let t=e.eventObject,n=t.__r3f;if(!(null!=n&&n.eventCount))return;let a=n.handlers;if(c){if(a.onPointerOver||a.onPointerEnter||a.onPointerOut||a.onPointerLeave){let t=eo(e),n=l.hovered.get(t);n?n.stopped&&e.stopPropagation():(l.hovered.set(t,e),null==a.onPointerOver||a.onPointerOver(e),null==a.onPointerEnter||a.onPointerEnter(e))}null==a.onPointerMove||a.onPointerMove(e)}else{let n=a[o];n?(!u||l.initialHits.includes(t))&&(r(i,l.interaction.filter(e=>!l.initialHits.includes(e))),n(e)):u&&l.initialHits.includes(t)&&r(i,l.interaction.filter(e=>!l.initialHits.includes(e)))}})}}}}(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(e$).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{let{set:n,events:r}=e.getState();if(null==r.disconnect||r.disconnect(),n(e=>({events:{...e.events,connected:t}})),r.handlers)for(let e in r.handlers){let n=r.handlers[e],[o,i]=e$[e];t.addEventListener(o,n,{passive:i})}},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){if(n.handlers)for(let e in n.handlers){let t=n.handlers[e],[r]=e$[e];n.connected.removeEventListener(r,t)}t(e=>({events:{...e.events,connected:void 0}}))}}}}},2941:(e,t,n)=>{n.d(t,{r:()=>a});var r=n(2115),o=n(2932);let i=(0,r.createContext)(null);function a({iterations:e=10,ms:t=250,threshold:n=.75,step:a=.1,factor:l=.5,flipflops:s=1/0,bounds:c=e=>e>100?[60,100]:[40,60],onIncline:u,onDecline:f,onChange:p,onFallback:d,children:h}){let[m,b]=(0,r.useState)(()=>({fps:0,index:0,factor:l,flipped:0,refreshrate:0,fallback:!1,frames:[],averages:[],subscriptions:new Map,subscribe:e=>{let t=Symbol();return m.subscriptions.set(t,e.current),()=>void m.subscriptions.delete(t)}})),v=0;return(0,o.C)(()=>{let{frames:r,averages:o}=m;if(!m.fallback&&o.length<e){r.push(performance.now());let i=r[r.length-1]-r[0];if(i>=t){if(m.fps=Math.round(r.length/i*1e3)/1,m.refreshrate=Math.max(m.refreshrate,m.fps),o[m.index++%e]=m.fps,o.length===e){let[t,r]=c(m.refreshrate),i=o.filter(e=>e>=r),l=o.filter(e=>e<t);i.length>e*n&&(m.factor=Math.min(1,m.factor+a),m.flipped++,u&&u(m),m.subscriptions.forEach(e=>e.onIncline&&e.onIncline(m))),l.length>e*n&&(m.factor=Math.max(0,m.factor-a),m.flipped++,f&&f(m),m.subscriptions.forEach(e=>e.onDecline&&e.onDecline(m))),v!==m.factor&&(v=m.factor,p&&p(m),m.subscriptions.forEach(e=>e.onChange&&e.onChange(m))),m.flipped>s&&!m.fallback&&(m.fallback=!0,d&&d(m),m.subscriptions.forEach(e=>e.onFallback&&e.onFallback(m))),m.averages=[]}m.frames=[]}}}),r.createElement(i.Provider,{value:m},h)}},4342:(e,t,n)=>{e.exports=n(7319)},4932:(e,t,n)=>{function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{N:()=>y});var o=n(2932),i=n(2115),a=n(3264),l=Object.defineProperty,s=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,c=(e,t,n)=>(s(e,"symbol"!=typeof t?t+"":t,n),n);class u{constructor(){c(this,"_listeners")}addEventListener(e,t){void 0===this._listeners&&(this._listeners={});let n=this._listeners;void 0===n[e]&&(n[e]=[]),-1===n[e].indexOf(t)&&n[e].push(t)}hasEventListener(e,t){if(void 0===this._listeners)return!1;let n=this._listeners;return void 0!==n[e]&&-1!==n[e].indexOf(t)}removeEventListener(e,t){if(void 0===this._listeners)return;let n=this._listeners[e];if(void 0!==n){let e=n.indexOf(t);-1!==e&&n.splice(e,1)}}dispatchEvent(e){if(void 0===this._listeners)return;let t=this._listeners[e.type];if(void 0!==t){e.target=this;let n=t.slice(0);for(let t=0,r=n.length;t<r;t++)n[t].call(this,e);e.target=null}}}var f=Object.defineProperty,p=(e,t,n)=>t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t,n)=>(p(e,"symbol"!=typeof t?t+"":t,n),n);let h=new a.RlV,m=new a.Zcv,b=Math.cos(Math.PI/180*70),v=(e,t)=>(e%t+t)%t;class g extends u{constructor(e,t){super(),d(this,"object"),d(this,"domElement"),d(this,"enabled",!0),d(this,"target",new a.Pq0),d(this,"minDistance",0),d(this,"maxDistance",1/0),d(this,"minZoom",0),d(this,"maxZoom",1/0),d(this,"minPolarAngle",0),d(this,"maxPolarAngle",Math.PI),d(this,"minAzimuthAngle",-1/0),d(this,"maxAzimuthAngle",1/0),d(this,"enableDamping",!1),d(this,"dampingFactor",.05),d(this,"enableZoom",!0),d(this,"zoomSpeed",1),d(this,"enableRotate",!0),d(this,"rotateSpeed",1),d(this,"enablePan",!0),d(this,"panSpeed",1),d(this,"screenSpacePanning",!0),d(this,"keyPanSpeed",7),d(this,"zoomToCursor",!1),d(this,"autoRotate",!1),d(this,"autoRotateSpeed",2),d(this,"reverseOrbit",!1),d(this,"reverseHorizontalOrbit",!1),d(this,"reverseVerticalOrbit",!1),d(this,"keys",{LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"}),d(this,"mouseButtons",{LEFT:a.kBv.ROTATE,MIDDLE:a.kBv.DOLLY,RIGHT:a.kBv.PAN}),d(this,"touches",{ONE:a.wtR.ROTATE,TWO:a.wtR.DOLLY_PAN}),d(this,"target0"),d(this,"position0"),d(this,"zoom0"),d(this,"_domElementKeyEvents",null),d(this,"getPolarAngle"),d(this,"getAzimuthalAngle"),d(this,"setPolarAngle"),d(this,"setAzimuthalAngle"),d(this,"getDistance"),d(this,"getZoomScale"),d(this,"listenToKeyEvents"),d(this,"stopListenToKeyEvents"),d(this,"saveState"),d(this,"reset"),d(this,"update"),d(this,"connect"),d(this,"dispose"),d(this,"dollyIn"),d(this,"dollyOut"),d(this,"getScale"),d(this,"setScale"),this.object=e,this.domElement=t,this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this.getPolarAngle=()=>u.phi,this.getAzimuthalAngle=()=>u.theta,this.setPolarAngle=e=>{let t=v(e,2*Math.PI),r=u.phi;r<0&&(r+=2*Math.PI),t<0&&(t+=2*Math.PI);let o=Math.abs(t-r);2*Math.PI-o<o&&(t<r?t+=2*Math.PI:r+=2*Math.PI),f.phi=t-r,n.update()},this.setAzimuthalAngle=e=>{let t=v(e,2*Math.PI),r=u.theta;r<0&&(r+=2*Math.PI),t<0&&(t+=2*Math.PI);let o=Math.abs(t-r);2*Math.PI-o<o&&(t<r?t+=2*Math.PI:r+=2*Math.PI),f.theta=t-r,n.update()},this.getDistance=()=>n.object.position.distanceTo(n.target),this.listenToKeyEvents=e=>{e.addEventListener("keydown",ee),this._domElementKeyEvents=e},this.stopListenToKeyEvents=()=>{this._domElementKeyEvents.removeEventListener("keydown",ee),this._domElementKeyEvents=null},this.saveState=()=>{n.target0.copy(n.target),n.position0.copy(n.object.position),n.zoom0=n.object.zoom},this.reset=()=>{n.target.copy(n.target0),n.object.position.copy(n.position0),n.object.zoom=n.zoom0,n.object.updateProjectionMatrix(),n.dispatchEvent(r),n.update(),s=l.NONE},this.update=(()=>{let t=new a.Pq0,o=new a.Pq0(0,1,0),i=new a.PTz().setFromUnitVectors(e.up,o),d=i.clone().invert(),v=new a.Pq0,y=new a.PTz,w=2*Math.PI;return function(){let j=n.object.position;i.setFromUnitVectors(e.up,o),d.copy(i).invert(),t.copy(j).sub(n.target),t.applyQuaternion(i),u.setFromVector3(t),n.autoRotate&&s===l.NONE&&L(2*Math.PI/60/60*n.autoRotateSpeed),n.enableDamping?(u.theta+=f.theta*n.dampingFactor,u.phi+=f.phi*n.dampingFactor):(u.theta+=f.theta,u.phi+=f.phi);let E=n.minAzimuthAngle,P=n.maxAzimuthAngle;isFinite(E)&&isFinite(P)&&(E<-Math.PI?E+=w:E>Math.PI&&(E-=w),P<-Math.PI?P+=w:P>Math.PI&&(P-=w),E<=P?u.theta=Math.max(E,Math.min(P,u.theta)):u.theta=u.theta>(E+P)/2?Math.max(E,u.theta):Math.min(P,u.theta)),u.phi=Math.max(n.minPolarAngle,Math.min(n.maxPolarAngle,u.phi)),u.makeSafe(),!0===n.enableDamping?n.target.addScaledVector(g,n.dampingFactor):n.target.add(g),n.zoomToCursor&&_||n.object.isOrthographicCamera?u.radius=F(u.radius):u.radius=F(u.radius*p),t.setFromSpherical(u),t.applyQuaternion(d),j.copy(n.target).add(t),n.object.matrixAutoUpdate||n.object.updateMatrix(),n.object.lookAt(n.target),!0===n.enableDamping?(f.theta*=1-n.dampingFactor,f.phi*=1-n.dampingFactor,g.multiplyScalar(1-n.dampingFactor)):(f.set(0,0,0),g.set(0,0,0));let x=!1;if(n.zoomToCursor&&_){let r=null;if(n.object instanceof a.ubm&&n.object.isPerspectiveCamera){let e=t.length();r=F(e*p);let o=e-r;n.object.position.addScaledVector(k,o),n.object.updateMatrixWorld()}else if(n.object.isOrthographicCamera){let e=new a.Pq0(T.x,T.y,0);e.unproject(n.object),n.object.zoom=Math.max(n.minZoom,Math.min(n.maxZoom,n.object.zoom/p)),n.object.updateProjectionMatrix(),x=!0;let o=new a.Pq0(T.x,T.y,0);o.unproject(n.object),n.object.position.sub(o).add(e),n.object.updateMatrixWorld(),r=t.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),n.zoomToCursor=!1;null!==r&&(n.screenSpacePanning?n.target.set(0,0,-1).transformDirection(n.object.matrix).multiplyScalar(r).add(n.object.position):(h.origin.copy(n.object.position),h.direction.set(0,0,-1).transformDirection(n.object.matrix),Math.abs(n.object.up.dot(h.direction))<b?e.lookAt(n.target):(m.setFromNormalAndCoplanarPoint(n.object.up,n.target),h.intersectPlane(m,n.target))))}else n.object instanceof a.qUd&&n.object.isOrthographicCamera&&(x=1!==p)&&(n.object.zoom=Math.max(n.minZoom,Math.min(n.maxZoom,n.object.zoom/p)),n.object.updateProjectionMatrix());return p=1,_=!1,!!(x||v.distanceToSquared(n.object.position)>c||8*(1-y.dot(n.object.quaternion))>c)&&(n.dispatchEvent(r),v.copy(n.object.position),y.copy(n.object.quaternion),x=!1,!0)}})(),this.connect=e=>{n.domElement=e,n.domElement.style.touchAction="none",n.domElement.addEventListener("contextmenu",et),n.domElement.addEventListener("pointerdown",$),n.domElement.addEventListener("pointercancel",Q),n.domElement.addEventListener("wheel",J)},this.dispose=()=>{var e,t,r,o,i,a;n.domElement&&(n.domElement.style.touchAction="auto"),null==(e=n.domElement)||e.removeEventListener("contextmenu",et),null==(t=n.domElement)||t.removeEventListener("pointerdown",$),null==(r=n.domElement)||r.removeEventListener("pointercancel",Q),null==(o=n.domElement)||o.removeEventListener("wheel",J),null==(i=n.domElement)||i.ownerDocument.removeEventListener("pointermove",G),null==(a=n.domElement)||a.ownerDocument.removeEventListener("pointerup",Q),null!==n._domElementKeyEvents&&n._domElementKeyEvents.removeEventListener("keydown",ee)};let n=this,r={type:"change"},o={type:"start"},i={type:"end"},l={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},s=l.NONE,c=1e-6,u=new a.YHV,f=new a.YHV,p=1,g=new a.Pq0,y=new a.I9Y,w=new a.I9Y,j=new a.I9Y,E=new a.I9Y,P=new a.I9Y,x=new a.I9Y,C=new a.I9Y,O=new a.I9Y,M=new a.I9Y,k=new a.Pq0,T=new a.I9Y,_=!1,S=[],A={};function I(){return Math.pow(.95,n.zoomSpeed)}function L(e){n.reverseOrbit||n.reverseHorizontalOrbit?f.theta+=e:f.theta-=e}function R(e){n.reverseOrbit||n.reverseVerticalOrbit?f.phi+=e:f.phi-=e}let z=(()=>{let e=new a.Pq0;return function(t,n){e.setFromMatrixColumn(n,0),e.multiplyScalar(-t),g.add(e)}})(),D=(()=>{let e=new a.Pq0;return function(t,r){!0===n.screenSpacePanning?e.setFromMatrixColumn(r,1):(e.setFromMatrixColumn(r,0),e.crossVectors(n.object.up,e)),e.multiplyScalar(t),g.add(e)}})(),N=(()=>{let e=new a.Pq0;return function(t,r){let o=n.domElement;if(o&&n.object instanceof a.ubm&&n.object.isPerspectiveCamera){let i=n.object.position;e.copy(i).sub(n.target);let a=e.length();z(2*t*(a*=Math.tan(n.object.fov/2*Math.PI/180))/o.clientHeight,n.object.matrix),D(2*r*a/o.clientHeight,n.object.matrix)}else o&&n.object instanceof a.qUd&&n.object.isOrthographicCamera?(z(t*(n.object.right-n.object.left)/n.object.zoom/o.clientWidth,n.object.matrix),D(r*(n.object.top-n.object.bottom)/n.object.zoom/o.clientHeight,n.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),n.enablePan=!1)}})();function H(e){n.object instanceof a.ubm&&n.object.isPerspectiveCamera||n.object instanceof a.qUd&&n.object.isOrthographicCamera?p=e:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),n.enableZoom=!1)}function Y(e){if(!n.zoomToCursor||!n.domElement)return;_=!0;let t=n.domElement.getBoundingClientRect(),r=e.clientX-t.left,o=e.clientY-t.top,i=t.width,a=t.height;T.x=r/i*2-1,T.y=-(o/a*2)+1,k.set(T.x,T.y,1).unproject(n.object).sub(n.object.position).normalize()}function F(e){return Math.max(n.minDistance,Math.min(n.maxDistance,e))}function q(e){y.set(e.clientX,e.clientY)}function U(e){E.set(e.clientX,e.clientY)}function B(){if(1==S.length)y.set(S[0].pageX,S[0].pageY);else{let e=.5*(S[0].pageX+S[1].pageX),t=.5*(S[0].pageY+S[1].pageY);y.set(e,t)}}function V(){if(1==S.length)E.set(S[0].pageX,S[0].pageY);else{let e=.5*(S[0].pageX+S[1].pageX),t=.5*(S[0].pageY+S[1].pageY);E.set(e,t)}}function X(){let e=S[0].pageX-S[1].pageX,t=S[0].pageY-S[1].pageY,n=Math.sqrt(e*e+t*t);C.set(0,n)}function Z(e){if(1==S.length)w.set(e.pageX,e.pageY);else{let t=er(e),n=.5*(e.pageX+t.x),r=.5*(e.pageY+t.y);w.set(n,r)}j.subVectors(w,y).multiplyScalar(n.rotateSpeed);let t=n.domElement;t&&(L(2*Math.PI*j.x/t.clientHeight),R(2*Math.PI*j.y/t.clientHeight)),y.copy(w)}function W(e){if(1==S.length)P.set(e.pageX,e.pageY);else{let t=er(e),n=.5*(e.pageX+t.x),r=.5*(e.pageY+t.y);P.set(n,r)}x.subVectors(P,E).multiplyScalar(n.panSpeed),N(x.x,x.y),E.copy(P)}function K(e){var t;let r=er(e),o=e.pageX-r.x,i=e.pageY-r.y,a=Math.sqrt(o*o+i*i);O.set(0,a),M.set(0,Math.pow(O.y/C.y,n.zoomSpeed)),t=M.y,H(p/t),C.copy(O)}function $(e){var t,r,i;!1!==n.enabled&&(0===S.length&&(null==(t=n.domElement)||t.ownerDocument.addEventListener("pointermove",G),null==(r=n.domElement)||r.ownerDocument.addEventListener("pointerup",Q)),i=e,S.push(i),"touch"===e.pointerType?function(e){switch(en(e),S.length){case 1:switch(n.touches.ONE){case a.wtR.ROTATE:if(!1===n.enableRotate)return;B(),s=l.TOUCH_ROTATE;break;case a.wtR.PAN:if(!1===n.enablePan)return;V(),s=l.TOUCH_PAN;break;default:s=l.NONE}break;case 2:switch(n.touches.TWO){case a.wtR.DOLLY_PAN:if(!1===n.enableZoom&&!1===n.enablePan)return;n.enableZoom&&X(),n.enablePan&&V(),s=l.TOUCH_DOLLY_PAN;break;case a.wtR.DOLLY_ROTATE:if(!1===n.enableZoom&&!1===n.enableRotate)return;n.enableZoom&&X(),n.enableRotate&&B(),s=l.TOUCH_DOLLY_ROTATE;break;default:s=l.NONE}break;default:s=l.NONE}s!==l.NONE&&n.dispatchEvent(o)}(e):function(e){let t;switch(e.button){case 0:t=n.mouseButtons.LEFT;break;case 1:t=n.mouseButtons.MIDDLE;break;case 2:t=n.mouseButtons.RIGHT;break;default:t=-1}switch(t){case a.kBv.DOLLY:if(!1===n.enableZoom)return;Y(e),C.set(e.clientX,e.clientY),s=l.DOLLY;break;case a.kBv.ROTATE:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===n.enablePan)return;U(e),s=l.PAN}else{if(!1===n.enableRotate)return;q(e),s=l.ROTATE}break;case a.kBv.PAN:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===n.enableRotate)return;q(e),s=l.ROTATE}else{if(!1===n.enablePan)return;U(e),s=l.PAN}break;default:s=l.NONE}s!==l.NONE&&n.dispatchEvent(o)}(e))}function G(e){!1!==n.enabled&&("touch"===e.pointerType?function(e){switch(en(e),s){case l.TOUCH_ROTATE:if(!1===n.enableRotate)return;Z(e),n.update();break;case l.TOUCH_PAN:if(!1===n.enablePan)return;W(e),n.update();break;case l.TOUCH_DOLLY_PAN:if(!1===n.enableZoom&&!1===n.enablePan)return;n.enableZoom&&K(e),n.enablePan&&W(e),n.update();break;case l.TOUCH_DOLLY_ROTATE:if(!1===n.enableZoom&&!1===n.enableRotate)return;n.enableZoom&&K(e),n.enableRotate&&Z(e),n.update();break;default:s=l.NONE}}(e):function(e){if(!1!==n.enabled)switch(s){case l.ROTATE:if(!1===n.enableRotate)return;w.set(e.clientX,e.clientY),j.subVectors(w,y).multiplyScalar(n.rotateSpeed);let t=n.domElement;t&&(L(2*Math.PI*j.x/t.clientHeight),R(2*Math.PI*j.y/t.clientHeight)),y.copy(w),n.update();break;case l.DOLLY:var r,o;if(!1===n.enableZoom)return;(O.set(e.clientX,e.clientY),M.subVectors(O,C),M.y>0)?(r=I(),H(p/r)):M.y<0&&(o=I(),H(p*o)),C.copy(O),n.update();break;case l.PAN:if(!1===n.enablePan)return;P.set(e.clientX,e.clientY),x.subVectors(P,E).multiplyScalar(n.panSpeed),N(x.x,x.y),E.copy(P),n.update()}}(e))}function Q(e){var t,r,o;(function(e){delete A[e.pointerId];for(let t=0;t<S.length;t++)if(S[t].pointerId==e.pointerId)return void S.splice(t,1)})(e),0===S.length&&(null==(t=n.domElement)||t.releasePointerCapture(e.pointerId),null==(r=n.domElement)||r.ownerDocument.removeEventListener("pointermove",G),null==(o=n.domElement)||o.ownerDocument.removeEventListener("pointerup",Q)),n.dispatchEvent(i),s=l.NONE}function J(e){if(!1!==n.enabled&&!1!==n.enableZoom&&(s===l.NONE||s===l.ROTATE)){var t,r;e.preventDefault(),n.dispatchEvent(o),(Y(e),e.deltaY<0)?(t=I(),H(p*t)):e.deltaY>0&&(r=I(),H(p/r)),n.update(),n.dispatchEvent(i)}}function ee(e){if(!1!==n.enabled&&!1!==n.enablePan){let t=!1;switch(e.code){case n.keys.UP:N(0,n.keyPanSpeed),t=!0;break;case n.keys.BOTTOM:N(0,-n.keyPanSpeed),t=!0;break;case n.keys.LEFT:N(n.keyPanSpeed,0),t=!0;break;case n.keys.RIGHT:N(-n.keyPanSpeed,0),t=!0}t&&(e.preventDefault(),n.update())}}function et(e){!1!==n.enabled&&e.preventDefault()}function en(e){let t=A[e.pointerId];void 0===t&&(t=new a.I9Y,A[e.pointerId]=t),t.set(e.pageX,e.pageY)}function er(e){return A[(e.pointerId===S[0].pointerId?S[1]:S[0]).pointerId]}this.dollyIn=(e=I())=>{H(p*e),n.update()},this.dollyOut=(e=I())=>{H(p/e),n.update()},this.getScale=()=>p,this.setScale=e=>{H(e),n.update()},this.getZoomScale=()=>I(),void 0!==t&&this.connect(t),this.update()}}let y=i.forwardRef(({makeDefault:e,camera:t,regress:n,domElement:a,enableDamping:l=!0,keyEvents:s=!1,onChange:c,onStart:u,onEnd:f,...p},d)=>{let h=(0,o.A)(e=>e.invalidate),m=(0,o.A)(e=>e.camera),b=(0,o.A)(e=>e.gl),v=(0,o.A)(e=>e.events),y=(0,o.A)(e=>e.setEvents),w=(0,o.A)(e=>e.set),j=(0,o.A)(e=>e.get),E=(0,o.A)(e=>e.performance),P=t||m,x=a||v.connected||b.domElement,C=i.useMemo(()=>new g(P),[P]);return(0,o.C)(()=>{C.enabled&&C.update()},-1),i.useEffect(()=>(s&&C.connect(!0===s?x:s),C.connect(x),()=>void C.dispose()),[s,x,n,C,h]),i.useEffect(()=>{let e=e=>{h(),n&&E.regress(),c&&c(e)},t=e=>{u&&u(e)},r=e=>{f&&f(e)};return C.addEventListener("change",e),C.addEventListener("start",t),C.addEventListener("end",r),()=>{C.removeEventListener("start",t),C.removeEventListener("end",r),C.removeEventListener("change",e)}},[c,u,f,C,h,y]),i.useEffect(()=>{if(e){let e=j().controls;return w({controls:C}),()=>w({controls:e})}},[e,C]),i.createElement("primitive",r({ref:d,object:C,enableDamping:l},p))})},5220:(e,t,n)=>{e.exports=n(1724)},5643:(e,t,n)=>{e.exports=n(6115)},6115:(e,t,n)=>{var r=n(2115),o=n(9033),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var f=l(null);if(null===f.current){var p={hasValue:!1,value:null};f.current=p}else p=f.current;var d=a(e,(f=c(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&p.hasValue){var t=p.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],f[1]);return s(function(){p.hasValue=!0,p.value=d},[d]),u(d),d}},6354:(e,t,n)=>{n.d(t,{Af:()=>l,Nz:()=>o,u5:()=>s,y3:()=>f});var r=n(2115);function o(e,t,n){if(!e)return;if(!0===n(e))return e;let r=t?e.return:e.child;for(;r;){let e=o(r,t,n);if(e)return e;r=t?null:r.sibling}}function i(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?r.useLayoutEffect:r.useEffect;let a=i(r.createContext(null));class l extends r.Component{render(){return r.createElement(a.Provider,{value:this._reactInternals},this.props.children)}}function s(){let e=r.useContext(a);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=r.useId();return r.useMemo(()=>{for(let n of[e,null==e?void 0:e.alternate]){if(!n)continue;let e=o(n,!1,e=>{let n=e.memoizedState;for(;n;){if(n.memoizedState===t)return!0;n=n.next}});if(e)return e}},[e,t])}let c=Symbol.for("react.context"),u=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===c;function f(){let e=function(){let e=s(),[t]=r.useState(()=>new Map);t.clear();let n=e;for(;n;){let e=n.type;u(e)&&e!==a&&!t.has(e)&&t.set(e,r.use(i(e))),n=n.return}return t}();return r.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>o=>r.createElement(t,null,r.createElement(n.Provider,{...o,value:e.get(n)})),e=>r.createElement(l,{...e})),[e])}},6500:(e,t)=>{t.ConcurrentRoot=1,t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},7319:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],p=1,d=null,h=3,m=!1,b=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function E(e){if(v=!1,j(e),!b)if(null!==r(u))b=!0,S();else{var t=r(f);null!==t&&A(E,t.startTime-e)}}var P=!1,x=-1,C=5,O=-1;function M(){return!(t.unstable_now()-O<C)}function k(){if(P){var e=t.unstable_now();O=e;var n=!0;try{e:{b=!1,v&&(v=!1,y(x),x=-1),m=!0;var i=h;try{t:{for(j(e),d=r(u);null!==d&&!(d.expirationTime>e&&M());){var l=d.callback;if("function"==typeof l){d.callback=null,h=d.priorityLevel;var s=l(d.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){d.callback=s,j(e),n=!0;break t}d===r(u)&&o(u),j(e)}else o(u);d=r(u)}if(null!==d)n=!0;else{var c=r(f);null!==c&&A(E,c.startTime-e),n=!1}}break e}finally{d=null,h=i,m=!1}}}finally{n?a():P=!1}}}if("function"==typeof w)a=function(){w(k)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,_=T.port2;T.port1.onmessage=k,a=function(){_.postMessage(null)}}else a=function(){g(k,0)};function S(){P||(P=!0,a())}function A(e,n){x=g(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||m||(b=!0,S())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:p++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(v?(y(x),x=-1):v=!0,A(E,i-a))):(e.sortIndex=l,n(u,e),b||m||(b=!0,S())),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},7558:(e,t,n)=>{n.d(t,{Hl:()=>p});var r=n(2932),o=n(2115),i=n(7431);function a(e,t){let n;return(...r)=>{window.clearTimeout(n),n=window.setTimeout(()=>e(...r),t)}}let l=["x","y","top","bottom","left","right","width","height"],s=(e,t)=>l.every(n=>e[n]===t[n]);var c=n(6354),u=n(5155);function f({ref:e,children:t,fallback:n,resize:l,style:c,gl:f,events:p=r.f,eventSource:d,eventPrefix:h,shadows:m,linear:b,flat:v,legacy:g,orthographic:y,frameloop:w,dpr:j,performance:E,raycaster:P,camera:x,scene:C,onPointerMissed:O,onCreated:M,...k}){o.useMemo(()=>(0,r.e)(i),[]);let T=(0,r.u)(),[_,S]=function({debounce:e,scroll:t,polyfill:n,offsetSize:r}={debounce:0,scroll:!1,offsetSize:!1}){var i,l,c;let u=n||("undefined"==typeof window?class{}:window.ResizeObserver);if(!u)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[f,p]=(0,o.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),d=(0,o.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:f,orientationHandler:null}),h=e?"number"==typeof e?e:e.scroll:null,m=e?"number"==typeof e?e:e.resize:null,b=(0,o.useRef)(!1);(0,o.useEffect)(()=>(b.current=!0,()=>void(b.current=!1)));let[v,g,y]=(0,o.useMemo)(()=>{let e=()=>{if(!d.current.element)return;let{left:e,top:t,width:n,height:o,bottom:i,right:a,x:l,y:c}=d.current.element.getBoundingClientRect(),u={left:e,top:t,width:n,height:o,bottom:i,right:a,x:l,y:c};d.current.element instanceof HTMLElement&&r&&(u.height=d.current.element.offsetHeight,u.width=d.current.element.offsetWidth),Object.freeze(u),b.current&&!s(d.current.lastBounds,u)&&p(d.current.lastBounds=u)};return[e,m?a(e,m):e,h?a(e,h):e]},[p,r,h,m]);function w(){d.current.scrollContainers&&(d.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",y,!0)),d.current.scrollContainers=null),d.current.resizeObserver&&(d.current.resizeObserver.disconnect(),d.current.resizeObserver=null),d.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",d.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",d.current.orientationHandler))}function j(){d.current.element&&(d.current.resizeObserver=new u(y),d.current.resizeObserver.observe(d.current.element),t&&d.current.scrollContainers&&d.current.scrollContainers.forEach(e=>e.addEventListener("scroll",y,{capture:!0,passive:!0})),d.current.orientationHandler=()=>{y()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",d.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",d.current.orientationHandler))}return i=y,l=!!t,(0,o.useEffect)(()=>{if(l)return window.addEventListener("scroll",i,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",i,!0)},[i,l]),c=g,(0,o.useEffect)(()=>(window.addEventListener("resize",c),()=>void window.removeEventListener("resize",c)),[c]),(0,o.useEffect)(()=>{w(),j()},[t,y,g]),(0,o.useEffect)(()=>w,[]),[e=>{e&&e!==d.current.element&&(w(),d.current.element=e,d.current.scrollContainers=function e(t){let n=[];if(!t||t===document.body)return n;let{overflow:r,overflowX:o,overflowY:i}=window.getComputedStyle(t);return[r,o,i].some(e=>"auto"===e||"scroll"===e)&&n.push(t),[...n,...e(t.parentElement)]}(e),j())},f,v]}({scroll:!0,debounce:{scroll:50,resize:0},...l}),A=o.useRef(null),I=o.useRef(null);o.useImperativeHandle(e,()=>A.current);let L=(0,r.a)(O),[R,z]=o.useState(!1),[D,N]=o.useState(!1);if(R)throw R;if(D)throw D;let H=o.useRef(null);(0,r.b)(()=>{let e=A.current;S.width>0&&S.height>0&&e&&(H.current||(H.current=(0,r.c)(e)),async function(){await H.current.configure({gl:f,scene:C,events:p,shadows:m,linear:b,flat:v,legacy:g,orthographic:y,frameloop:w,dpr:j,performance:E,raycaster:P,camera:x,size:S,onPointerMissed:(...e)=>null==L.current?void 0:L.current(...e),onCreated:e=>{null==e.events.connect||e.events.connect(d?(0,r.i)(d)?d.current:d:I.current),h&&e.setEvents({compute:(e,t)=>{let n=e[h+"X"],r=e[h+"Y"];t.pointer.set(n/t.size.width*2-1,-(2*(r/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==M||M(e)}}),H.current.render((0,u.jsx)(T,{children:(0,u.jsx)(r.E,{set:N,children:(0,u.jsx)(o.Suspense,{fallback:(0,u.jsx)(r.B,{set:z}),children:null!=t?t:null})})}))}())}),o.useEffect(()=>{let e=A.current;if(e)return()=>(0,r.d)(e)},[]);let Y=d?"none":"auto";return(0,u.jsx)("div",{ref:I,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:Y,...c},...k,children:(0,u.jsx)("div",{ref:_,style:{width:"100%",height:"100%"},children:(0,u.jsx)("canvas",{ref:A,style:{display:"block"},children:n})})})}function p(e){return(0,u.jsx)(c.Af,{children:(0,u.jsx)(f,{...e})})}n(1933),n(5220),n(4342)},8247:(e,t,n)=>{e.exports=n(620)},9033:(e,t,n)=>{e.exports=n(2436)}}]);