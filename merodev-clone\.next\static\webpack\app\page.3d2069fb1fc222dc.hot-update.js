"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/3d/AssetLoader.tsx":
/*!*******************************************!*\
  !*** ./src/components/3d/AssetLoader.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutBuilding: () => (/* binding */ AboutBuilding),\n/* harmony export */   AssetLoader: () => (/* binding */ AssetLoader),\n/* harmony export */   BackgroundElements: () => (/* binding */ BackgroundElements),\n/* harmony export */   ContactBuilding: () => (/* binding */ ContactBuilding),\n/* harmony export */   DecorativeElements: () => (/* binding */ DecorativeElements),\n/* harmony export */   ProjectsBuilding: () => (/* binding */ ProjectsBuilding),\n/* harmony export */   useAssetLoadingProgress: () => (/* binding */ useAssetLoadingProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/@react-three/fiber/dist/events-f681e724.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/constants */ \"(app-pages-browser)/./src/utils/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ AssetLoader,AboutBuilding,ProjectsBuilding,ContactBuilding,DecorativeElements,BackgroundElements,useAssetLoadingProgress auto */ \nvar _s = $RefreshSig$();\n\n// import { useGLTF } from '@react-three/drei'; // Will be used when we have actual GLTF models\n\n\nfunction BuildingModel(param) {\n    let { position, rotation = [\n        0,\n        0,\n        0\n    ], scale = [\n        1,\n        1,\n        1\n    ], color = '#ffffff', emissive = '#000000', fallbackGeometry = 'box', fallbackSize = [\n        2,\n        2,\n        2\n    ], animate = false } = param;\n    _s();\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Add subtle animation\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C)({\n        \"BuildingModel.useFrame\": (state)=>{\n            if (meshRef.current && animate) {\n                meshRef.current.rotation.y = rotation[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n                meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.8) * 0.05;\n            }\n        }\n    }[\"BuildingModel.useFrame\"]);\n    // For now, we'll use fallback geometry since we don't have actual models\n    // In the future, this would load the GLTF model:\n    // const { scene } = useGLTF(modelPath);\n    const renderFallbackGeometry = ()=>{\n        switch(fallbackGeometry){\n            case 'cylinder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cylinderGeometry\", {\n                    args: fallbackSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n            case 'sphere':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"sphereGeometry\", {\n                    args: [\n                        fallbackSize[0]\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                    args: fallbackSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        ref: meshRef,\n        position: position,\n        rotation: rotation,\n        scale: scale,\n        castShadow: true,\n        children: [\n            renderFallbackGeometry(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                color: color,\n                emissive: emissive,\n                roughness: 0.7,\n                metalness: 0.3\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(BuildingModel, \"/vg1AmA8+P3+Fj0/y210JTVKtL0=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C\n    ];\n});\n_c = BuildingModel;\n// Loading fallback component\nfunction LoadingFallback() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                args: [\n                    0.5,\n                    0.5,\n                    0.5\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshBasicMaterial\", {\n                color: \"#333333\",\n                wireframe: true\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoadingFallback;\nfunction AssetLoader(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n            lineNumber: 109,\n            columnNumber: 25\n        }, void 0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_c2 = AssetLoader;\n// Specific building components using enhanced positioning\nfunction AboutBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.about;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#00ffff\",\n        emissive: \"#001a1a\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            1.5,\n            2,\n            1.5\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c3 = AboutBuilding;\nfunction ProjectsBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.projects;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.projectsBuilding,\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#ff00ff\",\n        emissive: \"#1a001a\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            2,\n            3,\n            2\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_c4 = ProjectsBuilding;\nfunction ContactBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.contact;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.contactBuilding,\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#ffff00\",\n        emissive: \"#1a1a00\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            1.5,\n            2,\n            1.5\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_c5 = ContactBuilding;\nfunction DecorativeElements() {\n    const decorative1 = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.decorative1;\n    const decorative2 = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.decorative2;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    decorative1.x,\n                    decorative1.y,\n                    decorative1.z\n                ],\n                rotation: [\n                    decorative1.rotation.x,\n                    decorative1.rotation.y,\n                    decorative1.rotation.z\n                ],\n                scale: [\n                    decorative1.scale.x,\n                    decorative1.scale.y,\n                    decorative1.scale.z\n                ],\n                color: \"#ffffff\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"cylinder\",\n                fallbackSize: [\n                    0.3,\n                    1,\n                    0.3\n                ],\n                animate: false\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    decorative2.x,\n                    decorative2.y,\n                    decorative2.z\n                ],\n                rotation: [\n                    decorative2.rotation.x,\n                    decorative2.rotation.y,\n                    decorative2.rotation.z\n                ],\n                scale: [\n                    decorative2.scale.x,\n                    decorative2.scale.y,\n                    decorative2.scale.z\n                ],\n                color: \"#ffffff\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"cylinder\",\n                fallbackSize: [\n                    0.3,\n                    1,\n                    0.3\n                ],\n                animate: false\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c6 = DecorativeElements;\nfunction BackgroundElements() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: Object.entries(_utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS).filter((param)=>{\n            let [key] = param;\n            return key.startsWith('background');\n        }).map((param)=>{\n            let [key, config] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    config.x,\n                    config.y,\n                    config.z\n                ],\n                rotation: [\n                    config.rotation.x,\n                    config.rotation.y,\n                    config.rotation.z\n                ],\n                scale: [\n                    config.scale.x,\n                    config.scale.y,\n                    config.scale.z\n                ],\n                color: \"#0a0a0a\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"box\",\n                fallbackSize: [\n                    1,\n                    1,\n                    1\n                ],\n                animate: false\n            }, key, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 204,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false);\n}\n_c7 = BackgroundElements;\n// Hook for asset loading progress (future implementation)\nfunction useAssetLoadingProgress() {\n    // This would track loading progress of all assets\n    // For now, return mock data\n    return {\n        progress: 100,\n        isLoading: false,\n        error: null,\n        loadedAssets: [\n            'placeholder'\n        ]\n    };\n}\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"BuildingModel\");\n$RefreshReg$(_c1, \"LoadingFallback\");\n$RefreshReg$(_c2, \"AssetLoader\");\n$RefreshReg$(_c3, \"AboutBuilding\");\n$RefreshReg$(_c4, \"ProjectsBuilding\");\n$RefreshReg$(_c5, \"ContactBuilding\");\n$RefreshReg$(_c6, \"DecorativeElements\");\n$RefreshReg$(_c7, \"BackgroundElements\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/3d/AssetLoader.tsx\n"));

/***/ })

});