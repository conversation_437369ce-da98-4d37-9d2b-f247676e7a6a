"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/three-stdlib";
exports.ids = ["vendor-chunks/three-stdlib"];
exports.modules = {

/***/ "(ssr)/./node_modules/three-stdlib/controls/EventDispatcher.js":
/*!***************************************************************!*\
  !*** ./node_modules/three-stdlib/controls/EventDispatcher.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventDispatcher: () => (/* binding */ EventDispatcher)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass EventDispatcher {\n  constructor() {\n    // not defined in @types/three\n    __publicField(this, \"_listeners\");\n  }\n  /**\n   * Adds a listener to an event type.\n   * @param type The type of event to listen to.\n   * @param listener The function that gets called when the event is fired.\n   */\n  addEventListener(type, listener) {\n    if (this._listeners === void 0)\n      this._listeners = {};\n    const listeners = this._listeners;\n    if (listeners[type] === void 0) {\n      listeners[type] = [];\n    }\n    if (listeners[type].indexOf(listener) === -1) {\n      listeners[type].push(listener);\n    }\n  }\n  /**\n      * Checks if listener is added to an event type.\n      * @param type The type of event to listen to.\n      * @param listener The function that gets called when the event is fired.\n      */\n  hasEventListener(type, listener) {\n    if (this._listeners === void 0)\n      return false;\n    const listeners = this._listeners;\n    return listeners[type] !== void 0 && listeners[type].indexOf(listener) !== -1;\n  }\n  /**\n      * Removes a listener from an event type.\n      * @param type The type of the listener that gets removed.\n      * @param listener The listener function that gets removed.\n      */\n  removeEventListener(type, listener) {\n    if (this._listeners === void 0)\n      return;\n    const listeners = this._listeners;\n    const listenerArray = listeners[type];\n    if (listenerArray !== void 0) {\n      const index = listenerArray.indexOf(listener);\n      if (index !== -1) {\n        listenerArray.splice(index, 1);\n      }\n    }\n  }\n  /**\n      * Fire an event type.\n      * @param event The event that gets fired.\n      */\n  dispatchEvent(event) {\n    if (this._listeners === void 0)\n      return;\n    const listeners = this._listeners;\n    const listenerArray = listeners[event.type];\n    if (listenerArray !== void 0) {\n      event.target = this;\n      const array = listenerArray.slice(0);\n      for (let i = 0, l = array.length; i < l; i++) {\n        array[i].call(this, event);\n      }\n      event.target = null;\n    }\n  }\n}\n\n//# sourceMappingURL=EventDispatcher.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/controls/EventDispatcher.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/three-stdlib/controls/OrbitControls.js":
/*!*************************************************************!*\
  !*** ./node_modules/three-stdlib/controls/OrbitControls.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapControls: () => (/* binding */ MapControls),\n/* harmony export */   OrbitControls: () => (/* binding */ OrbitControls)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var _EventDispatcher_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EventDispatcher.js */ \"(ssr)/./node_modules/three-stdlib/controls/EventDispatcher.js\");\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\n\n\nconst _ray = /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Ray();\nconst _plane = /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Plane();\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180));\nconst moduloWrapAround = (offset, capacity) => (offset % capacity + capacity) % capacity;\nclass OrbitControls extends _EventDispatcher_js__WEBPACK_IMPORTED_MODULE_1__.EventDispatcher {\n  constructor(object, domElement) {\n    super();\n    __publicField(this, \"object\");\n    __publicField(this, \"domElement\");\n    // Set to false to disable this control\n    __publicField(this, \"enabled\", true);\n    // \"target\" sets the location of focus, where the object orbits around\n    __publicField(this, \"target\", new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n    // How far you can dolly in and out ( PerspectiveCamera only )\n    __publicField(this, \"minDistance\", 0);\n    __publicField(this, \"maxDistance\", Infinity);\n    // How far you can zoom in and out ( OrthographicCamera only )\n    __publicField(this, \"minZoom\", 0);\n    __publicField(this, \"maxZoom\", Infinity);\n    // How far you can orbit vertically, upper and lower limits.\n    // Range is 0 to Math.PI radians.\n    __publicField(this, \"minPolarAngle\", 0);\n    // radians\n    __publicField(this, \"maxPolarAngle\", Math.PI);\n    // radians\n    // How far you can orbit horizontally, upper and lower limits.\n    // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n    __publicField(this, \"minAzimuthAngle\", -Infinity);\n    // radians\n    __publicField(this, \"maxAzimuthAngle\", Infinity);\n    // radians\n    // Set to true to enable damping (inertia)\n    // If damping is enabled, you must call controls.update() in your animation loop\n    __publicField(this, \"enableDamping\", false);\n    __publicField(this, \"dampingFactor\", 0.05);\n    // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n    // Set to false to disable zooming\n    __publicField(this, \"enableZoom\", true);\n    __publicField(this, \"zoomSpeed\", 1);\n    // Set to false to disable rotating\n    __publicField(this, \"enableRotate\", true);\n    __publicField(this, \"rotateSpeed\", 1);\n    // Set to false to disable panning\n    __publicField(this, \"enablePan\", true);\n    __publicField(this, \"panSpeed\", 1);\n    __publicField(this, \"screenSpacePanning\", true);\n    // if false, pan orthogonal to world-space direction camera.up\n    __publicField(this, \"keyPanSpeed\", 7);\n    // pixels moved per arrow key push\n    __publicField(this, \"zoomToCursor\", false);\n    // Set to true to automatically rotate around the target\n    // If auto-rotate is enabled, you must call controls.update() in your animation loop\n    __publicField(this, \"autoRotate\", false);\n    __publicField(this, \"autoRotateSpeed\", 2);\n    // 30 seconds per orbit when fps is 60\n    __publicField(this, \"reverseOrbit\", false);\n    // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n    __publicField(this, \"reverseHorizontalOrbit\", false);\n    // true if you want to reverse the horizontal orbit direction\n    __publicField(this, \"reverseVerticalOrbit\", false);\n    // true if you want to reverse the vertical orbit direction\n    // The four arrow keys\n    __publicField(this, \"keys\", { LEFT: \"ArrowLeft\", UP: \"ArrowUp\", RIGHT: \"ArrowRight\", BOTTOM: \"ArrowDown\" });\n    // Mouse buttons\n    __publicField(this, \"mouseButtons\", {\n      LEFT: three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.ROTATE,\n      MIDDLE: three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.DOLLY,\n      RIGHT: three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.PAN\n    });\n    // Touch fingers\n    __publicField(this, \"touches\", { ONE: three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.ROTATE, TWO: three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_PAN });\n    __publicField(this, \"target0\");\n    __publicField(this, \"position0\");\n    __publicField(this, \"zoom0\");\n    // the target DOM element for key events\n    __publicField(this, \"_domElementKeyEvents\", null);\n    __publicField(this, \"getPolarAngle\");\n    __publicField(this, \"getAzimuthalAngle\");\n    __publicField(this, \"setPolarAngle\");\n    __publicField(this, \"setAzimuthalAngle\");\n    __publicField(this, \"getDistance\");\n    // Not used in most scenarios, however they can be useful for specific use cases\n    __publicField(this, \"getZoomScale\");\n    __publicField(this, \"listenToKeyEvents\");\n    __publicField(this, \"stopListenToKeyEvents\");\n    __publicField(this, \"saveState\");\n    __publicField(this, \"reset\");\n    __publicField(this, \"update\");\n    __publicField(this, \"connect\");\n    __publicField(this, \"dispose\");\n    // Dolly in programmatically\n    __publicField(this, \"dollyIn\");\n    // Dolly out programmatically\n    __publicField(this, \"dollyOut\");\n    // Get the current scale\n    __publicField(this, \"getScale\");\n    // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n    __publicField(this, \"setScale\");\n    this.object = object;\n    this.domElement = domElement;\n    this.target0 = this.target.clone();\n    this.position0 = this.object.position.clone();\n    this.zoom0 = this.object.zoom;\n    this.getPolarAngle = () => spherical.phi;\n    this.getAzimuthalAngle = () => spherical.theta;\n    this.setPolarAngle = (value) => {\n      let phi = moduloWrapAround(value, 2 * Math.PI);\n      let currentPhi = spherical.phi;\n      if (currentPhi < 0)\n        currentPhi += 2 * Math.PI;\n      if (phi < 0)\n        phi += 2 * Math.PI;\n      let phiDist = Math.abs(phi - currentPhi);\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI;\n        } else {\n          currentPhi += 2 * Math.PI;\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi;\n      scope.update();\n    };\n    this.setAzimuthalAngle = (value) => {\n      let theta = moduloWrapAround(value, 2 * Math.PI);\n      let currentTheta = spherical.theta;\n      if (currentTheta < 0)\n        currentTheta += 2 * Math.PI;\n      if (theta < 0)\n        theta += 2 * Math.PI;\n      let thetaDist = Math.abs(theta - currentTheta);\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI;\n        } else {\n          currentTheta += 2 * Math.PI;\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta;\n      scope.update();\n    };\n    this.getDistance = () => scope.object.position.distanceTo(scope.target);\n    this.listenToKeyEvents = (domElement2) => {\n      domElement2.addEventListener(\"keydown\", onKeyDown);\n      this._domElementKeyEvents = domElement2;\n    };\n    this.stopListenToKeyEvents = () => {\n      this._domElementKeyEvents.removeEventListener(\"keydown\", onKeyDown);\n      this._domElementKeyEvents = null;\n    };\n    this.saveState = () => {\n      scope.target0.copy(scope.target);\n      scope.position0.copy(scope.object.position);\n      scope.zoom0 = scope.object.zoom;\n    };\n    this.reset = () => {\n      scope.target.copy(scope.target0);\n      scope.object.position.copy(scope.position0);\n      scope.object.zoom = scope.zoom0;\n      scope.object.updateProjectionMatrix();\n      scope.dispatchEvent(changeEvent);\n      scope.update();\n      state = STATE.NONE;\n    };\n    this.update = (() => {\n      const offset = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      const up = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(0, 1, 0);\n      const quat = new three__WEBPACK_IMPORTED_MODULE_0__.Quaternion().setFromUnitVectors(object.up, up);\n      const quatInverse = quat.clone().invert();\n      const lastPosition = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      const lastQuaternion = new three__WEBPACK_IMPORTED_MODULE_0__.Quaternion();\n      const twoPI = 2 * Math.PI;\n      return function update() {\n        const position = scope.object.position;\n        quat.setFromUnitVectors(object.up, up);\n        quatInverse.copy(quat).invert();\n        offset.copy(position).sub(scope.target);\n        offset.applyQuaternion(quat);\n        spherical.setFromVector3(offset);\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle());\n        }\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor;\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor;\n        } else {\n          spherical.theta += sphericalDelta.theta;\n          spherical.phi += sphericalDelta.phi;\n        }\n        let min = scope.minAzimuthAngle;\n        let max = scope.maxAzimuthAngle;\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI)\n            min += twoPI;\n          else if (min > Math.PI)\n            min -= twoPI;\n          if (max < -Math.PI)\n            max += twoPI;\n          else if (max > Math.PI)\n            max -= twoPI;\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta));\n          } else {\n            spherical.theta = spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta);\n          }\n        }\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi));\n        spherical.makeSafe();\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor);\n        } else {\n          scope.target.add(panOffset);\n        }\n        if (scope.zoomToCursor && performCursorZoom || scope.object.isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius);\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale);\n        }\n        offset.setFromSpherical(spherical);\n        offset.applyQuaternion(quatInverse);\n        position.copy(scope.target).add(offset);\n        if (!scope.object.matrixAutoUpdate)\n          scope.object.updateMatrix();\n        scope.object.lookAt(scope.target);\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor;\n          sphericalDelta.phi *= 1 - scope.dampingFactor;\n          panOffset.multiplyScalar(1 - scope.dampingFactor);\n        } else {\n          sphericalDelta.set(0, 0, 0);\n          panOffset.set(0, 0, 0);\n        }\n        let zoomChanged = false;\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null;\n          if (scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            const prevRadius = offset.length();\n            newRadius = clampDistance(prevRadius * scale);\n            const radiusDelta = prevRadius - newRadius;\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta);\n            scope.object.updateMatrixWorld();\n          } else if (scope.object.isOrthographicCamera) {\n            const mouseBefore = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(mouse.x, mouse.y, 0);\n            mouseBefore.unproject(scope.object);\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale));\n            scope.object.updateProjectionMatrix();\n            zoomChanged = true;\n            const mouseAfter = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(mouse.x, mouse.y, 0);\n            mouseAfter.unproject(scope.object);\n            scope.object.position.sub(mouseAfter).add(mouseBefore);\n            scope.object.updateMatrixWorld();\n            newRadius = offset.length();\n          } else {\n            console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.\");\n            scope.zoomToCursor = false;\n          }\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              scope.target.set(0, 0, -1).transformDirection(scope.object.matrix).multiplyScalar(newRadius).add(scope.object.position);\n            } else {\n              _ray.origin.copy(scope.object.position);\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix);\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target);\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target);\n                _ray.intersectPlane(_plane, scope.target);\n              }\n            }\n          }\n        } else if (scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1;\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale));\n            scope.object.updateProjectionMatrix();\n          }\n        }\n        scale = 1;\n        performCursorZoom = false;\n        if (zoomChanged || lastPosition.distanceToSquared(scope.object.position) > EPS || 8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS) {\n          scope.dispatchEvent(changeEvent);\n          lastPosition.copy(scope.object.position);\n          lastQuaternion.copy(scope.object.quaternion);\n          zoomChanged = false;\n          return true;\n        }\n        return false;\n      };\n    })();\n    this.connect = (domElement2) => {\n      scope.domElement = domElement2;\n      scope.domElement.style.touchAction = \"none\";\n      scope.domElement.addEventListener(\"contextmenu\", onContextMenu);\n      scope.domElement.addEventListener(\"pointerdown\", onPointerDown);\n      scope.domElement.addEventListener(\"pointercancel\", onPointerUp);\n      scope.domElement.addEventListener(\"wheel\", onMouseWheel);\n    };\n    this.dispose = () => {\n      var _a, _b, _c, _d, _e, _f;\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = \"auto\";\n      }\n      (_a = scope.domElement) == null ? void 0 : _a.removeEventListener(\"contextmenu\", onContextMenu);\n      (_b = scope.domElement) == null ? void 0 : _b.removeEventListener(\"pointerdown\", onPointerDown);\n      (_c = scope.domElement) == null ? void 0 : _c.removeEventListener(\"pointercancel\", onPointerUp);\n      (_d = scope.domElement) == null ? void 0 : _d.removeEventListener(\"wheel\", onMouseWheel);\n      (_e = scope.domElement) == null ? void 0 : _e.ownerDocument.removeEventListener(\"pointermove\", onPointerMove);\n      (_f = scope.domElement) == null ? void 0 : _f.ownerDocument.removeEventListener(\"pointerup\", onPointerUp);\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener(\"keydown\", onKeyDown);\n      }\n    };\n    const scope = this;\n    const changeEvent = { type: \"change\" };\n    const startEvent = { type: \"start\" };\n    const endEvent = { type: \"end\" };\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6\n    };\n    let state = STATE.NONE;\n    const EPS = 1e-6;\n    const spherical = new three__WEBPACK_IMPORTED_MODULE_0__.Spherical();\n    const sphericalDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Spherical();\n    let scale = 1;\n    const panOffset = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n    const rotateStart = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const rotateEnd = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const rotateDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const panStart = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const panEnd = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const panDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyStart = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyEnd = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyDirection = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n    const mouse = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    let performCursorZoom = false;\n    const pointers = [];\n    const pointerPositions = {};\n    function getAutoRotationAngle() {\n      return 2 * Math.PI / 60 / 60 * scope.autoRotateSpeed;\n    }\n    function getZoomScale() {\n      return Math.pow(0.95, scope.zoomSpeed);\n    }\n    function rotateLeft(angle) {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle;\n      } else {\n        sphericalDelta.theta -= angle;\n      }\n    }\n    function rotateUp(angle) {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle;\n      } else {\n        sphericalDelta.phi -= angle;\n      }\n    }\n    const panLeft = (() => {\n      const v = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      return function panLeft2(distance, objectMatrix) {\n        v.setFromMatrixColumn(objectMatrix, 0);\n        v.multiplyScalar(-distance);\n        panOffset.add(v);\n      };\n    })();\n    const panUp = (() => {\n      const v = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      return function panUp2(distance, objectMatrix) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1);\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0);\n          v.crossVectors(scope.object.up, v);\n        }\n        v.multiplyScalar(distance);\n        panOffset.add(v);\n      };\n    })();\n    const pan = (() => {\n      const offset = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      return function pan2(deltaX, deltaY) {\n        const element = scope.domElement;\n        if (element && scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          const position = scope.object.position;\n          offset.copy(position).sub(scope.target);\n          let targetDistance = offset.length();\n          targetDistance *= Math.tan(scope.object.fov / 2 * Math.PI / 180);\n          panLeft(2 * deltaX * targetDistance / element.clientHeight, scope.object.matrix);\n          panUp(2 * deltaY * targetDistance / element.clientHeight, scope.object.matrix);\n        } else if (element && scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera && scope.object.isOrthographicCamera) {\n          panLeft(\n            deltaX * (scope.object.right - scope.object.left) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix\n          );\n          panUp(\n            deltaY * (scope.object.top - scope.object.bottom) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix\n          );\n        } else {\n          console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.\");\n          scope.enablePan = false;\n        }\n      };\n    })();\n    function setScale(newScale) {\n      if (scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.PerspectiveCamera && scope.object.isPerspectiveCamera || scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera && scope.object.isOrthographicCamera) {\n        scale = newScale;\n      } else {\n        console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.\");\n        scope.enableZoom = false;\n      }\n    }\n    function dollyOut(dollyScale) {\n      setScale(scale / dollyScale);\n    }\n    function dollyIn(dollyScale) {\n      setScale(scale * dollyScale);\n    }\n    function updateMouseParameters(event) {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return;\n      }\n      performCursorZoom = true;\n      const rect = scope.domElement.getBoundingClientRect();\n      const x = event.clientX - rect.left;\n      const y = event.clientY - rect.top;\n      const w = rect.width;\n      const h = rect.height;\n      mouse.x = x / w * 2 - 1;\n      mouse.y = -(y / h) * 2 + 1;\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize();\n    }\n    function clampDistance(dist) {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist));\n    }\n    function handleMouseDownRotate(event) {\n      rotateStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseDownDolly(event) {\n      updateMouseParameters(event);\n      dollyStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseDownPan(event) {\n      panStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseMoveRotate(event) {\n      rotateEnd.set(event.clientX, event.clientY);\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed);\n      const element = scope.domElement;\n      if (element) {\n        rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight);\n        rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight);\n      }\n      rotateStart.copy(rotateEnd);\n      scope.update();\n    }\n    function handleMouseMoveDolly(event) {\n      dollyEnd.set(event.clientX, event.clientY);\n      dollyDelta.subVectors(dollyEnd, dollyStart);\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale());\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale());\n      }\n      dollyStart.copy(dollyEnd);\n      scope.update();\n    }\n    function handleMouseMovePan(event) {\n      panEnd.set(event.clientX, event.clientY);\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);\n      pan(panDelta.x, panDelta.y);\n      panStart.copy(panEnd);\n      scope.update();\n    }\n    function handleMouseWheel(event) {\n      updateMouseParameters(event);\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale());\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale());\n      }\n      scope.update();\n    }\n    function handleKeyDown(event) {\n      let needsUpdate = false;\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n      }\n      if (needsUpdate) {\n        event.preventDefault();\n        scope.update();\n      }\n    }\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY);\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX);\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY);\n        rotateStart.set(x, y);\n      }\n    }\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY);\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX);\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY);\n        panStart.set(x, y);\n      }\n    }\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX;\n      const dy = pointers[0].pageY - pointers[1].pageY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      dollyStart.set(0, distance);\n    }\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom)\n        handleTouchStartDolly();\n      if (scope.enablePan)\n        handleTouchStartPan();\n    }\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom)\n        handleTouchStartDolly();\n      if (scope.enableRotate)\n        handleTouchStartRotate();\n    }\n    function handleTouchMoveRotate(event) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY);\n      } else {\n        const position = getSecondPointerPosition(event);\n        const x = 0.5 * (event.pageX + position.x);\n        const y = 0.5 * (event.pageY + position.y);\n        rotateEnd.set(x, y);\n      }\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed);\n      const element = scope.domElement;\n      if (element) {\n        rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight);\n        rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight);\n      }\n      rotateStart.copy(rotateEnd);\n    }\n    function handleTouchMovePan(event) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY);\n      } else {\n        const position = getSecondPointerPosition(event);\n        const x = 0.5 * (event.pageX + position.x);\n        const y = 0.5 * (event.pageY + position.y);\n        panEnd.set(x, y);\n      }\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);\n      pan(panDelta.x, panDelta.y);\n      panStart.copy(panEnd);\n    }\n    function handleTouchMoveDolly(event) {\n      const position = getSecondPointerPosition(event);\n      const dx = event.pageX - position.x;\n      const dy = event.pageY - position.y;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      dollyEnd.set(0, distance);\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));\n      dollyOut(dollyDelta.y);\n      dollyStart.copy(dollyEnd);\n    }\n    function handleTouchMoveDollyPan(event) {\n      if (scope.enableZoom)\n        handleTouchMoveDolly(event);\n      if (scope.enablePan)\n        handleTouchMovePan(event);\n    }\n    function handleTouchMoveDollyRotate(event) {\n      if (scope.enableZoom)\n        handleTouchMoveDolly(event);\n      if (scope.enableRotate)\n        handleTouchMoveRotate(event);\n    }\n    function onPointerDown(event) {\n      var _a, _b;\n      if (scope.enabled === false)\n        return;\n      if (pointers.length === 0) {\n        (_a = scope.domElement) == null ? void 0 : _a.ownerDocument.addEventListener(\"pointermove\", onPointerMove);\n        (_b = scope.domElement) == null ? void 0 : _b.ownerDocument.addEventListener(\"pointerup\", onPointerUp);\n      }\n      addPointer(event);\n      if (event.pointerType === \"touch\") {\n        onTouchStart(event);\n      } else {\n        onMouseDown(event);\n      }\n    }\n    function onPointerMove(event) {\n      if (scope.enabled === false)\n        return;\n      if (event.pointerType === \"touch\") {\n        onTouchMove(event);\n      } else {\n        onMouseMove(event);\n      }\n    }\n    function onPointerUp(event) {\n      var _a, _b, _c;\n      removePointer(event);\n      if (pointers.length === 0) {\n        (_a = scope.domElement) == null ? void 0 : _a.releasePointerCapture(event.pointerId);\n        (_b = scope.domElement) == null ? void 0 : _b.ownerDocument.removeEventListener(\"pointermove\", onPointerMove);\n        (_c = scope.domElement) == null ? void 0 : _c.ownerDocument.removeEventListener(\"pointerup\", onPointerUp);\n      }\n      scope.dispatchEvent(endEvent);\n      state = STATE.NONE;\n    }\n    function onMouseDown(event) {\n      let mouseAction;\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT;\n          break;\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE;\n          break;\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT;\n          break;\n        default:\n          mouseAction = -1;\n      }\n      switch (mouseAction) {\n        case three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.DOLLY:\n          if (scope.enableZoom === false)\n            return;\n          handleMouseDownDolly(event);\n          state = STATE.DOLLY;\n          break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false)\n              return;\n            handleMouseDownPan(event);\n            state = STATE.PAN;\n          } else {\n            if (scope.enableRotate === false)\n              return;\n            handleMouseDownRotate(event);\n            state = STATE.ROTATE;\n          }\n          break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false)\n              return;\n            handleMouseDownRotate(event);\n            state = STATE.ROTATE;\n          } else {\n            if (scope.enablePan === false)\n              return;\n            handleMouseDownPan(event);\n            state = STATE.PAN;\n          }\n          break;\n        default:\n          state = STATE.NONE;\n      }\n      if (state !== STATE.NONE) {\n        scope.dispatchEvent(startEvent);\n      }\n    }\n    function onMouseMove(event) {\n      if (scope.enabled === false)\n        return;\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false)\n            return;\n          handleMouseMoveRotate(event);\n          break;\n        case STATE.DOLLY:\n          if (scope.enableZoom === false)\n            return;\n          handleMouseMoveDolly(event);\n          break;\n        case STATE.PAN:\n          if (scope.enablePan === false)\n            return;\n          handleMouseMovePan(event);\n          break;\n      }\n    }\n    function onMouseWheel(event) {\n      if (scope.enabled === false || scope.enableZoom === false || state !== STATE.NONE && state !== STATE.ROTATE) {\n        return;\n      }\n      event.preventDefault();\n      scope.dispatchEvent(startEvent);\n      handleMouseWheel(event);\n      scope.dispatchEvent(endEvent);\n    }\n    function onKeyDown(event) {\n      if (scope.enabled === false || scope.enablePan === false)\n        return;\n      handleKeyDown(event);\n    }\n    function onTouchStart(event) {\n      trackPointer(event);\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.ROTATE:\n              if (scope.enableRotate === false)\n                return;\n              handleTouchStartRotate();\n              state = STATE.TOUCH_ROTATE;\n              break;\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.PAN:\n              if (scope.enablePan === false)\n                return;\n              handleTouchStartPan();\n              state = STATE.TOUCH_PAN;\n              break;\n            default:\n              state = STATE.NONE;\n          }\n          break;\n        case 2:\n          switch (scope.touches.TWO) {\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false)\n                return;\n              handleTouchStartDollyPan();\n              state = STATE.TOUCH_DOLLY_PAN;\n              break;\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false)\n                return;\n              handleTouchStartDollyRotate();\n              state = STATE.TOUCH_DOLLY_ROTATE;\n              break;\n            default:\n              state = STATE.NONE;\n          }\n          break;\n        default:\n          state = STATE.NONE;\n      }\n      if (state !== STATE.NONE) {\n        scope.dispatchEvent(startEvent);\n      }\n    }\n    function onTouchMove(event) {\n      trackPointer(event);\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false)\n            return;\n          handleTouchMoveRotate(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false)\n            return;\n          handleTouchMovePan(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false)\n            return;\n          handleTouchMoveDollyPan(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false)\n            return;\n          handleTouchMoveDollyRotate(event);\n          scope.update();\n          break;\n        default:\n          state = STATE.NONE;\n      }\n    }\n    function onContextMenu(event) {\n      if (scope.enabled === false)\n        return;\n      event.preventDefault();\n    }\n    function addPointer(event) {\n      pointers.push(event);\n    }\n    function removePointer(event) {\n      delete pointerPositions[event.pointerId];\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1);\n          return;\n        }\n      }\n    }\n    function trackPointer(event) {\n      let position = pointerPositions[event.pointerId];\n      if (position === void 0) {\n        position = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n        pointerPositions[event.pointerId] = position;\n      }\n      position.set(event.pageX, event.pageY);\n    }\n    function getSecondPointerPosition(event) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0];\n      return pointerPositions[pointer.pointerId];\n    }\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale);\n      scope.update();\n    };\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale);\n      scope.update();\n    };\n    this.getScale = () => {\n      return scale;\n    };\n    this.setScale = (newScale) => {\n      setScale(newScale);\n      scope.update();\n    };\n    this.getZoomScale = () => {\n      return getZoomScale();\n    };\n    if (domElement !== void 0)\n      this.connect(domElement);\n    this.update();\n  }\n}\nclass MapControls extends OrbitControls {\n  constructor(object, domElement) {\n    super(object, domElement);\n    this.screenSpacePanning = false;\n    this.mouseButtons.LEFT = three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.PAN;\n    this.mouseButtons.RIGHT = three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.ROTATE;\n    this.touches.ONE = three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.PAN;\n    this.touches.TWO = three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_ROTATE;\n  }\n}\n\n//# sourceMappingURL=OrbitControls.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/controls/OrbitControls.js\n");

/***/ })

};
;