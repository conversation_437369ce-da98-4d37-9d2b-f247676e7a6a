import { CameraConfig, LightConfig, ResponsiveConfig } from '@/types';

// Scene configuration
export const SCENE_CONFIG = {
  background: '#0a0a0a',
  fog: {
    color: '#0a0a0a',
    near: 10,
    far: 100,
  },
  shadows: {
    enabled: true,
    type: 'PCFSoftShadowMap',
  },
} as const;

// Isometric camera configuration
export const CAMERA_CONFIG: CameraConfig = {
  position: { x: 10, y: 10, z: 10 },
  target: { x: 0, y: 0, z: 0 },
  fov: 50,
  near: 0.1,
  far: 1000,
  enableControls: true,
  autoRotate: false,
};

// Lighting setup
export const LIGHT_CONFIG: LightConfig = {
  ambient: {
    color: '#404040',
    intensity: 0.4,
  },
  directional: {
    color: '#ffffff',
    intensity: 1,
    position: { x: 10, y: 10, z: 5 },
    castShadow: true,
  },
  spot: [
    {
      color: '#00ffff',
      intensity: 0.5,
      position: { x: 5, y: 8, z: 5 },
      target: { x: 0, y: 0, z: 0 },
      angle: Math.PI / 6,
      penumbra: 0.1,
    },
    {
      color: '#ff00ff',
      intensity: 0.3,
      position: { x: -5, y: 6, z: -5 },
      target: { x: 0, y: 0, z: 0 },
      angle: Math.PI / 8,
      penumbra: 0.2,
    },
  ],
};

// Building positions for the micro-city layout
export const BUILDING_POSITIONS = {
  about: { x: -4, y: 0, z: -2 },
  projects: { x: 0, y: 0, z: 0 },
  contact: { x: 4, y: 0, z: 2 },
  decorative1: { x: -2, y: 0, z: 4 },
  decorative2: { x: 2, y: 0, z: -4 },
} as const;

// Responsive breakpoints
export const RESPONSIVE_CONFIG: ResponsiveConfig = {
  mobile: {
    maxWidth: 768,
    camera: {
      ...CAMERA_CONFIG,
      position: { x: 15, y: 15, z: 15 },
      fov: 60,
    },
    ui: {
      scale: 0.8,
      touchEnabled: true,
    },
  },
  tablet: {
    maxWidth: 1024,
    camera: {
      ...CAMERA_CONFIG,
      position: { x: 12, y: 12, z: 12 },
      fov: 55,
    },
    ui: {
      scale: 0.9,
      touchEnabled: true,
    },
  },
  desktop: {
    minWidth: 1025,
    camera: CAMERA_CONFIG,
    ui: {
      scale: 1,
      touchEnabled: false,
    },
  },
};

// Animation durations
export const ANIMATION_DURATIONS = {
  hover: 0.3,
  click: 0.5,
  navigation: 1.2,
  sceneTransition: 2.0,
  glow: 2.0,
} as const;

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  targetFPS: 60,
  minFPS: 30,
  maxMemoryMB: 512,
  maxDrawCalls: 100,
  maxTriangles: 100000,
} as const;

// Asset paths
export const ASSET_PATHS = {
  models: {
    aboutBuilding: '/models/about-building.glb',
    projectsBuilding: '/models/projects-building.glb',
    contactBuilding: '/models/contact-building.glb',
    environment: '/models/environment.glb',
    decorative: '/models/decorative.glb',
  },
  textures: {
    ground: '/textures/ground.jpg',
    sky: '/textures/sky.jpg',
    glow: '/textures/glow.png',
  },
} as const;

// Shader configuration
export const SHADER_CONFIG = {
  glow: {
    vertexShader: `
      varying vec2 vUv;
      varying vec3 vNormal;
      varying vec3 vPosition;
      
      void main() {
        vUv = uv;
        vNormal = normalize(normalMatrix * normal);
        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform float time;
      uniform vec3 glowColor;
      uniform float glowIntensity;
      uniform float emissiveStrength;
      
      varying vec2 vUv;
      varying vec3 vNormal;
      varying vec3 vPosition;
      
      void main() {
        float fresnel = pow(1.0 - dot(vNormal, normalize(-vPosition)), 2.0);
        float pulse = sin(time * 2.0) * 0.5 + 0.5;
        vec3 glow = glowColor * glowIntensity * fresnel * (0.5 + pulse * 0.5);
        
        gl_FragColor = vec4(glow * emissiveStrength, 1.0);
      }
    `,
  },
} as const;

// UI configuration
export const UI_CONFIG = {
  overlay: {
    fadeInDuration: 0.5,
    fadeOutDuration: 0.3,
    maxWidth: '600px',
    padding: '2rem',
  },
  navigation: {
    transitionDuration: 0.8,
    easing: 'power2.inOut',
  },
  form: {
    validationDelay: 300,
    submitTimeout: 5000,
  },
} as const;

// Color palette
export const COLORS = {
  primary: '#00ffff',
  secondary: '#ff00ff',
  accent: '#ffff00',
  background: '#0a0a0a',
  surface: '#1a1a1a',
  text: '#ffffff',
  textSecondary: '#cccccc',
  error: '#ff4444',
  success: '#44ff44',
  warning: '#ffaa44',
} as const;

// Z-index layers
export const Z_INDEX = {
  background: 0,
  scene: 1,
  overlay: 10,
  modal: 20,
  tooltip: 30,
  loading: 40,
} as const;
