'use client';

import { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { PortfolioSection, InteractionEvent, PerformanceMetrics } from '@/types';

interface SceneContextType {
  // Current state
  currentSection: PortfolioSection;
  isLoading: boolean;
  loadingProgress: number;
  performanceMetrics: PerformanceMetrics | null;
  
  // Actions
  setCurrentSection: (section: PortfolioSection) => void;
  setLoading: (loading: boolean) => void;
  setLoadingProgress: (progress: number) => void;
  updatePerformanceMetrics: (metrics: PerformanceMetrics) => void;
  handleInteraction: (event: InteractionEvent) => void;
  
  // Settings
  showPerformanceMonitor: boolean;
  togglePerformanceMonitor: () => void;
}

const SceneContext = createContext<SceneContextType | null>(null);

interface SceneProviderProps {
  children: ReactNode;
}

export function SceneProvider({ children }: SceneProviderProps) {
  const [currentSection, setCurrentSection] = useState<PortfolioSection>('home');
  const [isLoading, setLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

  const updatePerformanceMetrics = useCallback((metrics: PerformanceMetrics) => {
    setPerformanceMetrics(metrics);
  }, []);

  const handleInteraction = useCallback((event: InteractionEvent) => {
    console.log('Scene interaction:', event);
    
    // Handle different interaction types
    switch (event.type) {
      case 'click':
        // Navigate to section based on target
        if (event.target.includes('about')) {
          setCurrentSection('about');
        } else if (event.target.includes('projects')) {
          setCurrentSection('projects');
        } else if (event.target.includes('contact')) {
          setCurrentSection('contact');
        }
        break;
      
      case 'hover':
        // Handle hover effects
        console.log('Hovering over:', event.target);
        break;
      
      case 'touch':
        // Handle touch interactions for mobile
        console.log('Touch interaction:', event.target);
        break;
    }
  }, []);

  const togglePerformanceMonitor = useCallback(() => {
    setShowPerformanceMonitor(prev => !prev);
  }, []);

  const contextValue: SceneContextType = {
    currentSection,
    isLoading,
    loadingProgress,
    performanceMetrics,
    setCurrentSection,
    setLoading,
    setLoadingProgress,
    updatePerformanceMetrics,
    handleInteraction,
    showPerformanceMonitor,
    togglePerformanceMonitor,
  };

  return (
    <SceneContext.Provider value={contextValue}>
      {children}
    </SceneContext.Provider>
  );
}

export function useScene() {
  const context = useContext(SceneContext);
  if (!context) {
    throw new Error('useScene must be used within a SceneProvider');
  }
  return context;
}
