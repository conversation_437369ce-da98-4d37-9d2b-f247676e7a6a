Here’s a polished **`requirements.md`** and a detailed **Requirements Plan** to guide an AI-powered build of a clone of **merodev.net**:

---

## 📄 requirements.md

```markdown
# Project Overview
Clone of merodev.net — immersive 3D, isometric "micro-city" portfolio showcasing sections (About, Projects, Contact) as interactive structures.

# Goals
- Recreate interactive 3D scene with isometric camera.
- Provide clickable zones that navigate to sections.
- Showcase performance optimizations (level of detail, batching).
- Integrate shaders for glow, animations, ambient lighting.
- Fully responsive across desktop and mobile.

# Tech Stack
- **Frontend**: React.js + Next.js (SSR & routing).
- **3D**: Three.js with React Three Fiber + Drei helpers.
- **Shaders**: GLSL for glow, emissive effects.
- **State / Animation**: React hooks + GSAP or Framer Motion.
- **Assets**: GLTF/GLB models (buildings, environment).
- **Textures**: PBR or stylized neon, clean textures.
- **UI Overlay**: React components (HTML overlay integrated in 3D).
- **Performance**:
  - Lazy-load model chunks.
  - Use instancing and LOD.
  - r3f-perf or PerformanceMonitor.
- **Responsive**:
  - Media queries for camera/UI.
  - Touch → hover events fallback.
- **SEO/Accessibility**:
  - SSR pages, meta tags.
  - Accessible interactive region labels.
- **Build & Deployment**:
  - Next.js build pipeline.
  - Vercel/Netlify deployment.
  - CDN for assets.

# Features
- **Initial Load**: Preloader, splash, fade into city.
- **Camera Controls**: OrbitControls restricted to isometric view.
- **Interactive Zones**: Hover glow & click scroll/navigate.
- **Animated Elements**: Glowing lights, small moving parts.
- **Section Integration**: HTML components aligned to world positions.
- **Contact Form**: Modal or section form with validation & submission.

# Development Phases
1. Setup project, routing, scene skeleton.
2. Add isometric camera + environment geometry.
3. Import 3D models and textures.
4. Implement interactivity: hover/click for navigation.
5. Add shaders for glow.
6. Build HTML overlays for About/Projects.
7. Add animations and performance layers.
8. Responsive & touch support.
9. SEO and accessibility improvements.
10. Deployment & optimizations.

# Deliverables
- Running clone website.
- Production-optimized codebase.
- Documentation: how to add sections, fade timing, performance tips.

```

---

## 🗂 Requirements Plan

| Phase                       | Description                                                       | AI Task Instructions                                                                                                                      |
| --------------------------- | ----------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------- |
| 1. Project Setup            | Initialize React + Next.js with TypeScript and React Three Fiber. | “Create a new Next.js TypeScript project and install `three`, `@react-three/fiber`, `@react-three/drei`, `gsap`, `r3f-perf`.”             |
| 2. Scene Skeleton           | Create an isometric camera setup and rendering canvas.            | “Set up `<Canvas>` in layout, configure PerspectiveCamera at isometric angle (≈30°), include ambient & spot lights.”                      |
| 3. 3D Models & Environment  | Import placeholder GLTF models and compose base city layout.      | “Use `useGLTF` to load multiple building GLTFs, position in grid, merge meshes or use instancing.”                                        |
| 4. Interactive Zones        | Enable hover/click interactivity.                                 | “Add `onPointerOver`, `onPointerOut`, `onClick` to meshes; change emissive intensity on hover; scroll to corresponding section on click.” |
| 5. Shader Glow              | Add emissive glow with custom shader.                             | “Write a simple glow shader using `ShaderMaterial`, applied to building edges or windows; animate glow with time uniform.”                |
| 6. HTML Overlays            | Overlay section content aligned with 3D zones.                    | “Use `<Html>` from Drei to embed React content at mesh position; set up About, Projects, Contact overlays with styling & fade-in.”        |
| 7. Animations & Performance | Add motion and optimize.                                          | “Animate lights blinking, small floating objects; integrate `PerformanceMonitor` and LOD/instancing.”                                     |
| 8. Responsiveness           | Support touch & mobile adjustments.                               | “Detect mobile via media-query; adjust camera, disable hover effects, expand click areas; ensure UI overlays scale.”                      |
| 9. SEO & Accessibility      | Improve markup and semantics.                                     | “Use Next.js metadata API for titles/descriptions; add `aria-label` to clickable meshes; ensure keyboard navigation through zones.”       |
| 10. Deployment              | Configure build & hosting.                                        | “Set Next.js export to static; optimize assets; configure thumbnails and deploy to Vercel with environment variables if needed.”          |

---

## Supporting References

* React Three Fiber + Drei: declarative 3D in React ([linkedin.com](https://www.linkedin.com/posts/merouanebali_frontenddevelopment-threejs-webdev-activity-7274720959763288065-Fu5R?utm_source=chatgpt.com), [github.com](https://github.com/pmndrs/react-three-fiber?utm_source=chatgpt.com), [youtube.com](https://www.youtube.com/watch?v=bD9WTZV1sSg&utm_source=chatgpt.com), [techhub.iodigital.com](https://techhub.iodigital.com/articles/react-three-fiber-3d-for-the-web?utm_source=chatgpt.com), [linkedin.com](https://www.linkedin.com/posts/awejosepholaitan_3d-webdesign-webdev-activity-7239602510175358976-Y4ZX?utm_source=chatgpt.com), [reddit.com](https://www.reddit.com/r/webdev/comments/1agztma/3d_website_tech_stack_for_2024/?utm_source=chatgpt.com))
* Use of PerformanceMonitor, instancing, LOD: best practices ()
* Typical stack for 3D portfolios: React/Three.js with Blender assets ([reddit.com](https://www.reddit.com/r/webdev/comments/1agztma/3d_website_tech_stack_for_2024/?utm_source=chatgpt.com))

---

### ✅ Next Steps

1. Feed `requirements.md` and phase breakdown to your AI assistant.
2. Execute phase-by-phase: scaffold → import → interactivity → polish.
3. Regularly test performance (FPS, bundle size).
4. Deploy and iterate on UX and asset optimization.

Let me know if you want code snippets, shader examples, 3D model guidance, or integration steps for HTML overlays!
