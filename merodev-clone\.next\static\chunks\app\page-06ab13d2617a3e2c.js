(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5896:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>M});var o=t(5155),r=t(7558),s=t(4932),i=t(2115),n=t(2932);let l={about:{x:-4,y:1,z:-2,rotation:{x:0,y:.2,z:0},scale:{x:1,y:1,z:1}},projects:{x:0,y:1.5,z:0,rotation:{x:0,y:0,z:0},scale:{x:1.1,y:1.2,z:1.1}},contact:{x:4,y:1,z:2,rotation:{x:0,y:-.2,z:0},scale:{x:1,y:1,z:1}},decorative1:{x:-2,y:.5,z:4,rotation:{x:0,y:0,z:0},scale:{x:1,y:1,z:1}},decorative2:{x:2,y:.5,z:-4,rotation:{x:0,y:0,z:0},scale:{x:1,y:1,z:1}},background1:{x:-6,y:.5,z:-6,rotation:{x:0,y:.5,z:0},scale:{x:.8,y:.8,z:.8}},background2:{x:6,y:.5,z:6,rotation:{x:0,y:-.5,z:0},scale:{x:.8,y:.8,z:.8}},background3:{x:-6,y:.5,z:6,rotation:{x:0,y:.3,z:0},scale:{x:.8,y:.8,z:.8}},background4:{x:6,y:.5,z:-6,rotation:{x:0,y:-.3,z:0},scale:{x:.8,y:.8,z:.8}}},c={targetFPS:60,minFPS:30},d={models:{aboutBuilding:"/models/about-building.glb",projectsBuilding:"/models/projects-building.glb",contactBuilding:"/models/contact-building.glb",environment:"/models/environment.glb",decorative:"/models/decorative.glb"}};function x(e){let{modelPath:a,position:t,rotation:r=[0,0,0],scale:s=[1,1,1],color:l="#ffffff",emissive:c="#000000",fallbackGeometry:d="box",fallbackSize:x=[2,2,2],animate:m=!1}=e,h=(0,i.useRef)(null);return(0,n.C)(e=>{h.current&&m&&(h.current.rotation.y=r[1]+.1*Math.sin(.5*e.clock.elapsedTime),h.current.position.y=t[1]+.05*Math.sin(.8*e.clock.elapsedTime))}),(0,o.jsxs)("mesh",{ref:h,position:t,rotation:r,scale:s,castShadow:!0,children:[(()=>{switch(d){case"cylinder":return(0,o.jsx)("cylinderGeometry",{args:x});case"sphere":return(0,o.jsx)("sphereGeometry",{args:[x[0]]});default:return(0,o.jsx)("boxGeometry",{args:x})}})(),(0,o.jsx)("meshStandardMaterial",{color:l,emissive:c,roughness:.7,metalness:.3})]})}function m(){return(0,o.jsxs)("mesh",{children:[(0,o.jsx)("boxGeometry",{args:[.5,.5,.5]}),(0,o.jsx)("meshBasicMaterial",{color:"#333333",wireframe:!0})]})}function h(e){let{children:a}=e;return(0,o.jsx)(i.Suspense,{fallback:(0,o.jsx)(m,{}),children:a})}function f(){let e=l.about;return(0,o.jsx)(x,{modelPath:d.models.aboutBuilding,position:[e.x,e.y,e.z],rotation:[e.rotation.x,e.rotation.y,e.rotation.z],scale:[e.scale.x,e.scale.y,e.scale.z],color:"#00ffff",emissive:"#001a1a",fallbackGeometry:"box",fallbackSize:[1.5,2,1.5],animate:!0})}function u(){let e=l.projects;return(0,o.jsx)(x,{modelPath:d.models.projectsBuilding,position:[e.x,e.y,e.z],rotation:[e.rotation.x,e.rotation.y,e.rotation.z],scale:[e.scale.x,e.scale.y,e.scale.z],color:"#ff00ff",emissive:"#1a001a",fallbackGeometry:"box",fallbackSize:[2,3,2],animate:!0})}function y(){let e=l.contact;return(0,o.jsx)(x,{modelPath:d.models.contactBuilding,position:[e.x,e.y,e.z],rotation:[e.rotation.x,e.rotation.y,e.rotation.z],scale:[e.scale.x,e.scale.y,e.scale.z],color:"#ffff00",emissive:"#1a1a00",fallbackGeometry:"box",fallbackSize:[1.5,2,1.5],animate:!0})}function j(){let e=l.decorative1,a=l.decorative2;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(x,{modelPath:d.models.decorative,position:[e.x,e.y,e.z],rotation:[e.rotation.x,e.rotation.y,e.rotation.z],scale:[e.scale.x,e.scale.y,e.scale.z],color:"#ffffff",emissive:"#000000",fallbackGeometry:"cylinder",fallbackSize:[.3,1,.3],animate:!1}),(0,o.jsx)(x,{modelPath:d.models.decorative,position:[a.x,a.y,a.z],rotation:[a.rotation.x,a.rotation.y,a.rotation.z],scale:[a.scale.x,a.scale.y,a.scale.z],color:"#ffffff",emissive:"#000000",fallbackGeometry:"cylinder",fallbackSize:[.3,1,.3],animate:!1})]})}function b(){return(0,o.jsx)(o.Fragment,{children:Object.entries(l).filter(e=>{let[a]=e;return a.startsWith("background")}).map(e=>{let[a,t]=e;return(0,o.jsx)(x,{modelPath:d.models.decorative,position:[t.x,t.y,t.z],rotation:[t.rotation.x,t.rotation.y,t.rotation.z],scale:[t.scale.x,t.scale.y,t.scale.z],color:"#0a0a0a",emissive:"#000000",fallbackGeometry:"box",fallbackSize:[1,1,1],animate:!1},a)})})}var g=t(2941),p=t(3264);function z(e){let{count:a=20}=e,t=(0,i.useRef)(null),r=(0,i.useMemo)(()=>{let e=[];for(let t=0;t<a;t++)e.push([(Math.random()-.5)*30,.2,(Math.random()-.5)*30]);return e},[a]);return(0,n.C)(e=>{if(t.current){let o=e.clock.elapsedTime;for(let e=0;e<a;e++){let a=new p.kn4,[s,i,n]=r[e];a.setPosition(s,i+.1*Math.sin(o+e),n),t.current.setMatrixAt(e,a)}t.current.instanceMatrix.needsUpdate=!0}}),(0,o.jsxs)("instancedMesh",{ref:t,args:[void 0,void 0,a],children:[(0,o.jsx)("cylinderGeometry",{args:[.1,.1,.4]}),(0,o.jsx)("meshBasicMaterial",{color:"#444444"})]})}function v(e){let{children:a}=e,{gl:t,scene:r}=(0,n.A)(),s=(0,i.useRef)(1),l=e=>{let a=e.fps;a<c.minFPS?(s.current=Math.max(.5,s.current-.1),t.setPixelRatio(Math.min(window.devicePixelRatio,s.current)),a<20&&r.traverse(e=>{e instanceof p.eaF&&(e.castShadow=!1,e.receiveShadow=!1)})):a>.9*c.targetFPS&&(s.current=Math.min(1,s.current+.05),t.setPixelRatio(Math.min(window.devicePixelRatio,s.current)))};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(g.r,{onIncline:l,onDecline:l}),a]})}function w(){let e=(0,i.useRef)(null),a=(0,i.useMemo)(()=>new p.bdM(20,20,4,4),[]);return(0,o.jsxs)("mesh",{ref:e,rotation:[-Math.PI/2,0,0],position:[0,-1,0],receiveShadow:!0,children:[(0,o.jsx)("primitive",{object:a}),(0,o.jsx)("meshLambertMaterial",{color:"#1a1a1a"})]})}function S(){let{gl:e}=(0,n.A)(),a=(0,i.useRef)(null);return(0,n.C)(()=>{if(a.current){let t=e.info;a.current.innerHTML='\n        <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; font-family: monospace; font-size: 12px; z-index: 1000;">\n          <div>Triangles: '.concat(t.render.triangles,"</div>\n          <div>Draw Calls: ").concat(t.render.calls,"</div>\n          <div>Geometries: ").concat(t.memory.geometries,"</div>\n          <div>Textures: ").concat(t.memory.textures,"</div>\n        </div>\n      ")}}),(0,o.jsx)("div",{ref:a})}function M(){return(0,o.jsx)("main",{className:"w-full h-screen overflow-hidden",children:(0,o.jsxs)(r.Hl,{camera:{position:[10,10,10],fov:50},shadows:!0,gl:{antialias:!0,powerPreference:"high-performance",alpha:!1,stencil:!1},dpr:[1,2],performance:{min:.5},children:[(0,o.jsx)("color",{attach:"background",args:["#0a0a0a"]}),(0,o.jsx)("fog",{attach:"fog",args:["#0a0a0a",10,100]}),(0,o.jsxs)(v,{children:[(0,o.jsx)("ambientLight",{intensity:.4,color:"#404040"}),(0,o.jsx)("directionalLight",{position:[10,10,5],intensity:1,castShadow:!0,"shadow-mapSize-width":2048,"shadow-mapSize-height":2048,"shadow-camera-far":50,"shadow-camera-left":-10,"shadow-camera-right":10,"shadow-camera-top":10,"shadow-camera-bottom":-10}),(0,o.jsx)("spotLight",{position:[5,8,5],color:"#00ffff",intensity:.5,angle:Math.PI/6,penumbra:.1,decay:2,distance:30}),(0,o.jsx)("spotLight",{position:[-5,6,-5],color:"#ff00ff",intensity:.3,angle:Math.PI/8,penumbra:.2,decay:2,distance:25}),(0,o.jsx)(s.N,{enablePan:!0,enableZoom:!0,enableRotate:!0,minDistance:5,maxDistance:50}),(0,o.jsxs)(h,{children:[(0,o.jsx)(f,{}),(0,o.jsx)(u,{}),(0,o.jsx)(y,{}),(0,o.jsx)(j,{}),(0,o.jsx)(b,{})]}),(0,o.jsx)(z,{count:15}),(0,o.jsx)(w,{}),(0,o.jsxs)("mesh",{position:[-4,-.8,-2],receiveShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[3,.4,3]}),(0,o.jsx)("meshStandardMaterial",{color:"#2a2a2a"})]}),(0,o.jsxs)("mesh",{position:[0,-.8,0],receiveShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[4,.4,4]}),(0,o.jsx)("meshStandardMaterial",{color:"#2a2a2a"})]}),(0,o.jsxs)("mesh",{position:[4,-.8,2],receiveShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[3,.4,3]}),(0,o.jsx)("meshStandardMaterial",{color:"#2a2a2a"})]}),(0,o.jsxs)("mesh",{position:[-2,-.9,-1],receiveShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[2,.2,.5]}),(0,o.jsx)("meshStandardMaterial",{color:"#333333"})]}),(0,o.jsxs)("mesh",{position:[2,-.9,1],receiveShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[2,.2,.5]}),(0,o.jsx)("meshStandardMaterial",{color:"#333333"})]}),(0,o.jsxs)("mesh",{position:[-8,.5,-8],castShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[1,1,1]}),(0,o.jsx)("meshStandardMaterial",{color:"#0a0a0a"})]}),(0,o.jsxs)("mesh",{position:[8,.5,8],castShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[1,1,1]}),(0,o.jsx)("meshStandardMaterial",{color:"#0a0a0a"})]}),(0,o.jsxs)("mesh",{position:[-8,.5,8],castShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[1,1,1]}),(0,o.jsx)("meshStandardMaterial",{color:"#0a0a0a"})]}),(0,o.jsxs)("mesh",{position:[8,.5,-8],castShadow:!0,children:[(0,o.jsx)("boxGeometry",{args:[1,1,1]}),(0,o.jsx)("meshStandardMaterial",{color:"#0a0a0a"})]})]}),(0,o.jsx)(S,{})]})})}},7285:(e,a,t)=>{Promise.resolve().then(t.bind(t,5896))}},e=>{var a=a=>e(e.s=a);e.O(0,[367,831,413,239,441,684,358],()=>a(7285)),_N_E=e.O()}]);