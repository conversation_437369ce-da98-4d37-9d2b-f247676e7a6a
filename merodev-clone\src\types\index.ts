// Core 3D types
export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

export interface BuildingData {
  id: string;
  name: string;
  position: Vector3;
  rotation: Vector3;
  scale: Vector3;
  modelPath: string;
  section: PortfolioSection;
  isInteractive: boolean;
}

export interface InteractionZone {
  id: string;
  buildingId: string;
  bounds: {
    min: Vector3;
    max: Vector3;
  };
  action: InteractionAction;
}

// Portfolio sections
export type PortfolioSection = 'about' | 'projects' | 'contact' | 'home';

export interface SectionData {
  id: PortfolioSection;
  title: string;
  description: string;
  content: React.ReactNode;
  position: Vector3;
}

// Interaction types
export type InteractionAction = 'navigate' | 'highlight' | 'modal' | 'scroll';

export interface InteractionEvent {
  type: 'hover' | 'click' | 'touch';
  target: string;
  position: Vector3;
  timestamp: number;
}

// Performance monitoring
export interface PerformanceMetrics {
  fps: number;
  memory: number;
  drawCalls: number;
  triangles: number;
  loadTime: number;
}

// Camera configuration
export interface CameraConfig {
  position: Vector3;
  target: Vector3;
  fov: number;
  near: number;
  far: number;
  enableControls: boolean;
  autoRotate: boolean;
}

// Lighting configuration
export interface LightConfig {
  ambient: {
    color: string;
    intensity: number;
  };
  directional: {
    color: string;
    intensity: number;
    position: Vector3;
    castShadow: boolean;
  };
  spot: {
    color: string;
    intensity: number;
    position: Vector3;
    target: Vector3;
    angle: number;
    penumbra: number;
  }[];
}

// Shader uniforms
export interface ShaderUniforms {
  time: { value: number };
  glowColor: { value: [number, number, number] };
  glowIntensity: { value: number };
  emissiveStrength: { value: number };
}

// Asset loading
export interface AssetLoadingState {
  isLoading: boolean;
  progress: number;
  error: string | null;
  loadedAssets: string[];
}

// Responsive breakpoints
export interface ResponsiveConfig {
  mobile: {
    maxWidth: number;
    camera: CameraConfig;
    ui: {
      scale: number;
      touchEnabled: boolean;
    };
  };
  tablet: {
    maxWidth: number;
    camera: CameraConfig;
    ui: {
      scale: number;
      touchEnabled: boolean;
    };
  };
  desktop: {
    minWidth: number;
    camera: CameraConfig;
    ui: {
      scale: number;
      touchEnabled: boolean;
    };
  };
}

// Animation configuration
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay: number;
  repeat: number;
  yoyo: boolean;
}

// Project data structure
export interface ProjectData {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  imageUrl: string;
  demoUrl?: string;
  githubUrl?: string;
  featured: boolean;
}

// Contact form
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactFormState {
  data: ContactFormData;
  isSubmitting: boolean;
  isSubmitted: boolean;
  error: string | null;
}
