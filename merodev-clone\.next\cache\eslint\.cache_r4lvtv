[{"C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\layout.tsx": "1", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\page.tsx": "2", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Camera.tsx": "3", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Lighting.tsx": "4", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Scene.tsx": "5", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\SceneProvider.tsx": "6", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\ui\\LoadingScreen.tsx": "7", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\types\\index.ts": "8", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\utils\\constants.ts": "9"}, {"size": 689, "mtime": 1751451811535, "results": "10", "hashOfConfig": "11"}, {"size": 1111, "mtime": 1751511978988, "results": "12", "hashOfConfig": "11"}, {"size": 3345, "mtime": 1751512120739, "results": "13", "hashOfConfig": "11"}, {"size": 2860, "mtime": 1751511955539, "results": "14", "hashOfConfig": "11"}, {"size": 2025, "mtime": 1751476578505, "results": "15", "hashOfConfig": "11"}, {"size": 3001, "mtime": 1751476608485, "results": "16", "hashOfConfig": "11"}, {"size": 2864, "mtime": 1751476593348, "results": "17", "hashOfConfig": "11"}, {"size": 3115, "mtime": 1751476517240, "results": "18", "hashOfConfig": "11"}, {"size": 4533, "mtime": 1751476542869, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "34qxw2", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\layout.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\page.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Camera.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Lighting.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Scene.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\SceneProvider.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\ui\\LoadingScreen.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\types\\index.ts", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\utils\\constants.ts", [], []]