[{"C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\layout.tsx": "1", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\page.tsx": "2", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Camera.tsx": "3", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Lighting.tsx": "4", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Scene.tsx": "5", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\SceneProvider.tsx": "6", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\ui\\LoadingScreen.tsx": "7", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\types\\index.ts": "8", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\utils\\constants.ts": "9", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\AssetLoader.tsx": "10", "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\PerformanceOptimizer.tsx": "11"}, {"size": 689, "mtime": 1751451811535, "results": "12", "hashOfConfig": "13"}, {"size": 4322, "mtime": 1751513245776, "results": "14", "hashOfConfig": "13"}, {"size": 3358, "mtime": 1751512320497, "results": "15", "hashOfConfig": "13"}, {"size": 2860, "mtime": 1751511955539, "results": "16", "hashOfConfig": "13"}, {"size": 2025, "mtime": 1751476578505, "results": "17", "hashOfConfig": "13"}, {"size": 3001, "mtime": 1751476608485, "results": "18", "hashOfConfig": "13"}, {"size": 2864, "mtime": 1751476593348, "results": "19", "hashOfConfig": "13"}, {"size": 3115, "mtime": 1751476517240, "results": "20", "hashOfConfig": "13"}, {"size": 5408, "mtime": 1751513021492, "results": "21", "hashOfConfig": "13"}, {"size": 6805, "mtime": 1751513320138, "results": "22", "hashOfConfig": "13"}, {"size": 6059, "mtime": 1751513333415, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "34qxw2", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\layout.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\page.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Camera.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Lighting.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\Scene.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\SceneProvider.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\ui\\LoadingScreen.tsx", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\types\\index.ts", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\utils\\constants.ts", [], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\AssetLoader.tsx", ["57"], [], "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\PerformanceOptimizer.tsx", [], [], {"ruleId": "58", "severity": 2, "message": "59", "line": 27, "column": 14, "nodeType": null, "messageId": "60", "endLine": 27, "endColumn": 24}, "@typescript-eslint/no-unused-vars", "'_modelPath' is defined but never used.", "unusedVar"]