'use client';

import { useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { Detailed, PerformanceMonitor } from '@react-three/drei';
import { PERFORMANCE_THRESHOLDS } from '@/utils/constants';
import * as THREE from 'three';

interface LODBuilding {
  position: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  color: string;
  emissive?: string;
}

// Level of Detail component for buildings
export function LODBuilding({ 
  position, 
  rotation = [0, 0, 0], 
  scale = [1, 1, 1], 
  color, 
  emissive = '#000000' 
}: LODBuilding) {
  return (
    <Detailed distances={[0, 15, 25, 35]}>
      {/* High detail (close) */}
      <mesh position={position} rotation={rotation} scale={scale} castShadow receiveShadow>
        <boxGeometry args={[2, 3, 2]} />
        <meshStandardMaterial 
          color={color} 
          emissive={emissive}
          roughness={0.7}
          metalness={0.3}
        />
      </mesh>
      
      {/* Medium detail */}
      <mesh position={position} rotation={rotation} scale={scale} castShadow>
        <boxGeometry args={[2, 3, 2]} />
        <meshLambertMaterial color={color} emissive={emissive} />
      </mesh>
      
      {/* Low detail */}
      <mesh position={position} rotation={rotation} scale={scale}>
        <boxGeometry args={[2, 3, 2]} />
        <meshBasicMaterial color={color} />
      </mesh>
      
      {/* Very low detail (far) */}
      <mesh position={position} rotation={rotation} scale={scale}>
        <boxGeometry args={[1, 2, 1]} />
        <meshBasicMaterial color={color} />
      </mesh>
    </Detailed>
  );
}

// Instanced decorative elements for better performance
export function InstancedDecorations({ count = 20 }: { count?: number }) {
  const meshRef = useRef<THREE.InstancedMesh>(null);
  
  const positions = useMemo(() => {
    const temp = [];
    for (let i = 0; i < count; i++) {
      temp.push([
        (Math.random() - 0.5) * 30,
        0.2,
        (Math.random() - 0.5) * 30
      ]);
    }
    return temp;
  }, [count]);

  useFrame((state) => {
    if (meshRef.current) {
      const time = state.clock.elapsedTime;
      for (let i = 0; i < count; i++) {
        const matrix = new THREE.Matrix4();
        const [x, y, z] = positions[i];
        matrix.setPosition(
          x,
          y + Math.sin(time + i) * 0.1,
          z
        );
        meshRef.current.setMatrixAt(i, matrix);
      }
      meshRef.current.instanceMatrix.needsUpdate = true;
    }
  });

  return (
    <instancedMesh ref={meshRef} args={[undefined, undefined, count]}>
      <cylinderGeometry args={[0.1, 0.1, 0.4]} />
      <meshBasicMaterial color="#444444" />
    </instancedMesh>
  );
}

// Performance monitoring and adaptive quality
interface AdaptiveQualityProps {
  children: React.ReactNode;
}

export function AdaptiveQuality({ children }: AdaptiveQualityProps) {
  const { gl, scene } = useThree();
  const qualityRef = useRef(1);
  
  const handlePerformanceChange = (api: { fps: number }) => {
    const fps = api.fps;
    
    if (fps < PERFORMANCE_THRESHOLDS.minFPS) {
      // Reduce quality
      qualityRef.current = Math.max(0.5, qualityRef.current - 0.1);
      gl.setPixelRatio(Math.min(window.devicePixelRatio, qualityRef.current));
      
      // Disable shadows if performance is very poor
      if (fps < 20) {
        scene.traverse((object) => {
          if (object instanceof THREE.Mesh) {
            object.castShadow = false;
            object.receiveShadow = false;
          }
        });
      }
    } else if (fps > PERFORMANCE_THRESHOLDS.targetFPS * 0.9) {
      // Increase quality
      qualityRef.current = Math.min(1, qualityRef.current + 0.05);
      gl.setPixelRatio(Math.min(window.devicePixelRatio, qualityRef.current));
    }
  };

  return (
    <>
      <PerformanceMonitor 
        onIncline={handlePerformanceChange}
        onDecline={handlePerformanceChange}
      />
      {children}
    </>
  );
}

// Frustum culling helper
export function FrustumCulling({ children }: { children: React.ReactNode }) {
  const { camera } = useThree();
  const frustum = useMemo(() => new THREE.Frustum(), []);
  const cameraMatrix = useMemo(() => new THREE.Matrix4(), []);

  useFrame(() => {
    cameraMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    frustum.setFromProjectionMatrix(cameraMatrix);
  });

  return <>{children}</>;
}

// Optimized ground with reduced geometry
export function OptimizedGround() {
  const groundRef = useRef<THREE.Mesh>(null);
  
  // Use lower resolution geometry for better performance
  const geometry = useMemo(() => {
    return new THREE.PlaneGeometry(20, 20, 4, 4);
  }, []);

  return (
    <mesh 
      ref={groundRef}
      rotation={[-Math.PI / 2, 0, 0]} 
      position={[0, -1, 0]} 
      receiveShadow
    >
      <primitive object={geometry} />
      <meshLambertMaterial color="#1a1a1a" />
    </mesh>
  );
}

// Memory management utilities
export function useMemoryManagement() {
  const { gl } = useThree();
  
  const cleanupMemory = () => {
    // Force garbage collection of GPU resources
    gl.dispose();
    
    // Clear any cached geometries and materials
    THREE.Cache.clear();
  };

  return { cleanupMemory };
}

// Performance stats display
export function PerformanceStats() {
  const { gl } = useThree();
  const statsRef = useRef<HTMLDivElement>(null);

  useFrame(() => {
    if (statsRef.current) {
      const info = gl.info;
      statsRef.current.innerHTML = `
        <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; font-family: monospace; font-size: 12px; z-index: 1000;">
          <div>Triangles: ${info.render.triangles}</div>
          <div>Draw Calls: ${info.render.calls}</div>
          <div>Geometries: ${info.memory.geometries}</div>
          <div>Textures: ${info.memory.textures}</div>
        </div>
      `;
    }
  });

  return <div ref={statsRef} />;
}
