'use client';

import { useState, useEffect } from 'react';
import { COLORS, Z_INDEX } from '@/utils/constants';

interface LoadingScreenProps {
  progress?: number;
  message?: string;
}

export function LoadingScreen({ 
  progress = 0, 
  message = 'Loading 3D Environment...' 
}: LoadingScreenProps) {
  const [displayProgress, setDisplayProgress] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDisplayProgress(progress);
    }, 100);

    return () => clearTimeout(timer);
  }, [progress]);

  return (
    <div 
      className="fixed inset-0 flex items-center justify-center bg-black"
      style={{ zIndex: Z_INDEX.loading }}
    >
      <div className="text-center space-y-6">
        {/* Logo or title */}
        <div className="mb-8">
          <h1 
            className="text-4xl font-bold mb-2"
            style={{ color: COLORS.primary }}
          >
            Portfolio
          </h1>
          <p 
            className="text-lg"
            style={{ color: COLORS.textSecondary }}
          >
            3D Interactive Experience
          </p>
        </div>

        {/* Loading spinner */}
        <div className="relative w-16 h-16 mx-auto mb-6">
          <div 
            className="absolute inset-0 border-4 border-gray-800 rounded-full"
          />
          <div 
            className="absolute inset-0 border-4 border-transparent rounded-full animate-spin"
            style={{ 
              borderTopColor: COLORS.primary,
              borderRightColor: COLORS.secondary,
            }}
          />
        </div>

        {/* Progress bar */}
        <div className="w-64 mx-auto">
          <div 
            className="h-1 bg-gray-800 rounded-full overflow-hidden"
          >
            <div 
              className="h-full transition-all duration-300 ease-out rounded-full"
              style={{ 
                width: `${displayProgress}%`,
                background: `linear-gradient(90deg, ${COLORS.primary}, ${COLORS.secondary})`,
              }}
            />
          </div>
          <p 
            className="text-sm mt-2"
            style={{ color: COLORS.textSecondary }}
          >
            {Math.round(displayProgress)}%
          </p>
        </div>

        {/* Loading message */}
        <p 
          className="text-sm animate-pulse"
          style={{ color: COLORS.text }}
        >
          {message}
        </p>

        {/* Animated dots */}
        <div className="flex justify-center space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 rounded-full animate-bounce"
              style={{ 
                backgroundColor: COLORS.primary,
                animationDelay: `${i * 0.2}s`,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
