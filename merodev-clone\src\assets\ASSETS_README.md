# 3D Assets Requirements

## Required 3D Models (GLTF/GLB format)

### Buildings
- **about-building.glb** - Modern office building representing the About section
  - Dimensions: ~3x4x3 units
  - Style: Clean, modern architecture
  - Features: Windows, entrance, subtle details
  - Optimization: <50k triangles

- **projects-building.glb** - Tech/creative building for Projects section
  - Dimensions: ~4x5x4 units
  - Style: Contemporary with tech elements
  - Features: Large windows, modern facade, LED accents
  - Optimization: <75k triangles

- **contact-building.glb** - Communication tower/building for Contact section
  - Dimensions: ~3x4x3 units
  - Style: Communication hub with antenna elements
  - Features: Satellite dishes, communication arrays
  - Optimization: <50k triangles

### Environment Elements
- **environment-base.glb** - Ground textures and base elements
  - Features: Textured ground, pathways, basic landscaping
  - Optimization: <25k triangles

- **decorative-elements.glb** - Small decorative objects
  - Features: Street lights, small structures, details
  - Optimization: <10k triangles each

### Textures (1024x1024 or 512x512)
- **ground-texture.jpg** - Concrete/asphalt ground texture
- **building-windows.jpg** - Glass and window reflections
- **metal-panels.jpg** - Building facade materials
- **glow-mask.png** - Alpha mask for glow effects

## Asset Creation Guidelines

### Technical Requirements
- Format: GLTF 2.0 (.glb binary format preferred)
- Textures: Power-of-2 dimensions (512x512, 1024x1024)
- Materials: PBR workflow (Albedo, Normal, Roughness, Metallic)
- Optimization: Use texture atlasing where possible
- LOD: Consider multiple detail levels for performance

### Style Guidelines
- **Color Palette**: 
  - Primary: Cyan (#00ffff), Magenta (#ff00ff), Yellow (#ffff00)
  - Neutrals: Dark grays (#1a1a1a, #2a2a2a), White (#ffffff)
  - Background: Very dark (#0a0a0a)

- **Lighting**: 
  - Emissive materials for glow effects
  - Metallic surfaces for reflections
  - Matte surfaces for contrast

- **Architecture Style**:
  - Clean, modern lines
  - Minimal but distinctive
  - Isometric-friendly proportions
  - Clear silhouettes

## Temporary Placeholders

Currently using basic Three.js geometry as placeholders:
- BoxGeometry for buildings
- PlaneGeometry for ground
- CylinderGeometry for decorative elements

## Asset Sources

### Free Resources
- **Sketchfab** - Free CC0 architectural models
- **Poly Haven** - Free PBR textures and HDRIs
- **Kenney Assets** - Free low-poly building blocks
- **Quaternius** - Free low-poly models

### Creation Tools
- **Blender** - Free 3D modeling and texturing
- **Substance Painter** - Professional texturing (paid)
- **GIMP/Photoshop** - Texture editing

## Implementation Notes

1. **Loading Strategy**: Use React Three Fiber's `useGLTF` hook with preloading
2. **Performance**: Implement instancing for repeated elements
3. **Interactivity**: Ensure models have proper naming for click detection
4. **Animations**: Consider simple animations (rotation, scaling) for visual interest

## File Structure
```
src/assets/
├── models/
│   ├── about-building.glb
│   ├── projects-building.glb
│   ├── contact-building.glb
│   ├── environment-base.glb
│   └── decorative-elements.glb
├── textures/
│   ├── ground-texture.jpg
│   ├── building-windows.jpg
│   ├── metal-panels.jpg
│   └── glow-mask.png
└── ASSETS_README.md
```

## Next Steps

1. Create or source the required 3D models
2. Optimize models for web performance
3. Implement asset loading with progress tracking
4. Add proper materials and textures
5. Test performance across different devices
