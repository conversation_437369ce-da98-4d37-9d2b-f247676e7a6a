'use client';

import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import {
  AssetLoader,
  AboutBuilding,
  ProjectsBuilding,
  ContactBuilding,
  DecorativeElements,
  BackgroundElements
} from '@/components/3d/AssetLoader';
import {
  AdaptiveQuality,
  InstancedDecorations,
  OptimizedGround,
  PerformanceStats
} from '@/components/3d/PerformanceOptimizer';

export default function Home() {
  return (
    <main className="w-full h-screen overflow-hidden">
      <Canvas
        camera={{ position: [10, 10, 10], fov: 50 }}
        shadows
        gl={{
          antialias: true,
          powerPreference: 'high-performance',
          alpha: false,
          stencil: false
        }}
        dpr={[1, 2]}
        performance={{ min: 0.5 }}
      >
        {/* Scene background and atmosphere */}
        <color attach="background" args={['#0a0a0a']} />
        <fog attach="fog" args={['#0a0a0a', 10, 100]} />

        {/* Performance optimization wrapper */}
        <AdaptiveQuality>
        {/* Enhanced lighting system */}
        <ambientLight intensity={0.4} color="#404040" />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <spotLight
          position={[5, 8, 5]}
          color="#00ffff"
          intensity={0.5}
          angle={Math.PI / 6}
          penumbra={0.1}
          decay={2}
          distance={30}
        />
        <spotLight
          position={[-5, 6, -5]}
          color="#ff00ff"
          intensity={0.3}
          angle={Math.PI / 8}
          penumbra={0.2}
          decay={2}
          distance={25}
        />

        {/* Camera controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={50}
        />

          {/* Portfolio section buildings with asset loading */}
          <AssetLoader>
            <AboutBuilding />
            <ProjectsBuilding />
            <ContactBuilding />
            <DecorativeElements />
            <BackgroundElements />
          </AssetLoader>

          {/* Instanced decorations for performance */}
          <InstancedDecorations count={15} />

          {/* Optimized ground plane */}
          <OptimizedGround />

        {/* City blocks/platforms */}
        <mesh position={[-4, -0.8, -2]} receiveShadow>
          <boxGeometry args={[3, 0.4, 3]} />
          <meshStandardMaterial color="#2a2a2a" />
        </mesh>

        <mesh position={[0, -0.8, 0]} receiveShadow>
          <boxGeometry args={[4, 0.4, 4]} />
          <meshStandardMaterial color="#2a2a2a" />
        </mesh>

        <mesh position={[4, -0.8, 2]} receiveShadow>
          <boxGeometry args={[3, 0.4, 3]} />
          <meshStandardMaterial color="#2a2a2a" />
        </mesh>

        {/* Connecting pathways */}
        <mesh position={[-2, -0.9, -1]} receiveShadow>
          <boxGeometry args={[2, 0.2, 0.5]} />
          <meshStandardMaterial color="#333333" />
        </mesh>

        <mesh position={[2, -0.9, 1]} receiveShadow>
          <boxGeometry args={[2, 0.2, 0.5]} />
          <meshStandardMaterial color="#333333" />
        </mesh>

        {/* Background elements */}
        <mesh position={[-8, 0.5, -8]} castShadow>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#0a0a0a" />
        </mesh>

        <mesh position={[8, 0.5, 8]} castShadow>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#0a0a0a" />
        </mesh>

        <mesh position={[-8, 0.5, 8]} castShadow>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#0a0a0a" />
        </mesh>

          <mesh position={[8, 0.5, -8]} castShadow>
            <boxGeometry args={[1, 1, 1]} />
            <meshStandardMaterial color="#0a0a0a" />
          </mesh>
        </AdaptiveQuality>

        {/* Performance monitoring */}
        <PerformanceStats />
      </Canvas>
    </main>
  );
}
