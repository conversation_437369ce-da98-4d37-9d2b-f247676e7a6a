'use client';

import { SceneProvider } from '@/components/3d/SceneProvider';
import { Scene } from '@/components/3d/Scene';
import { Lighting } from '@/components/3d/Lighting';

export default function Home() {
  return (
    <SceneProvider>
      <main className="w-full h-screen overflow-hidden">
        <Scene showPerformanceMonitor={false}>
          {/* Lighting setup */}
          <Lighting />

          {/* Temporary placeholder - we'll add 3D content in the next steps */}
          <mesh position={[0, 0, 0]} castShadow>
            <boxGeometry args={[2, 2, 2]} />
            <meshStandardMaterial color="#00ffff" />
          </mesh>

          {/* Ground plane */}
          <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
            <planeGeometry args={[20, 20]} />
            <meshStandardMaterial color="#1a1a1a" />
          </mesh>
        </Scene>
      </main>
    </SceneProvider>
  );
}
