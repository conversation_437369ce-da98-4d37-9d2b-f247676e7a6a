'use client';

import { useRef, useEffect } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { CAMERA_CONFIG, RESPONSIVE_CONFIG } from '@/utils/constants';
import { useScene } from './SceneProvider';
import * as THREE from 'three';

// Import the actual OrbitControls type from three-stdlib
import { OrbitControls as OrbitControlsImpl } from 'three-stdlib';

interface CameraProps {
  enableControls?: boolean;
  autoRotate?: boolean;
}

export function Camera({ enableControls = true, autoRotate = false }: CameraProps) {
  const { camera, size } = useThree();
  const controlsRef = useRef<OrbitControlsImpl>(null);
  const { currentSection } = useScene();

  // Configure camera for isometric view
  useEffect(() => {
    if (camera instanceof THREE.PerspectiveCamera) {
      // Set isometric-like position (30-degree angle)
      const distance = 20;
      const angle = Math.PI / 6; // 30 degrees
      
      camera.position.set(
        distance * Math.cos(angle),
        distance * Math.sin(angle),
        distance * Math.cos(angle)
      );
      
      camera.lookAt(0, 0, 0);
      camera.fov = CAMERA_CONFIG.fov;
      camera.near = CAMERA_CONFIG.near;
      camera.far = CAMERA_CONFIG.far;
      camera.updateProjectionMatrix();
    }
  }, [camera]);

  // Responsive camera adjustments
  useEffect(() => {
    const handleResize = () => {
      if (camera instanceof THREE.PerspectiveCamera) {
        const width = size.width;
        let config = RESPONSIVE_CONFIG.desktop.camera;
        
        if (width <= RESPONSIVE_CONFIG.mobile.maxWidth) {
          config = RESPONSIVE_CONFIG.mobile.camera;
        } else if (width <= RESPONSIVE_CONFIG.tablet.maxWidth) {
          config = RESPONSIVE_CONFIG.tablet.camera;
        }
        
        camera.fov = config.fov;
        camera.position.set(config.position.x, config.position.y, config.position.z);
        camera.updateProjectionMatrix();
      }
    };

    handleResize();
  }, [camera, size]);

  // Animate camera based on current section
  useFrame(() => {
    if (controlsRef.current && currentSection !== 'home') {
      const targetPositions = {
        about: { x: -2, y: 0, z: -1 },
        projects: { x: 0, y: 0, z: 0 },
        contact: { x: 2, y: 0, z: 1 },
      };

      const target = targetPositions[currentSection as keyof typeof targetPositions];
      if (target) {
        // Smooth camera transition to focus on the selected section
        controlsRef.current.target.lerp(
          new THREE.Vector3(target.x, target.y, target.z),
          0.05
        );
        controlsRef.current.update();
      }
    }
  });

  if (!enableControls) {
    return null;
  }

  return (
    <OrbitControls
      ref={controlsRef}
      enablePan={true}
      enableZoom={true}
      enableRotate={true}
      autoRotate={autoRotate}
      autoRotateSpeed={0.5}
      minDistance={10}
      maxDistance={50}
      minPolarAngle={Math.PI / 6} // 30 degrees
      maxPolarAngle={Math.PI / 2.5} // ~72 degrees (maintain isometric feel)
      minAzimuthAngle={-Math.PI / 4} // -45 degrees
      maxAzimuthAngle={Math.PI / 4} // 45 degrees
      dampingFactor={0.05}
      enableDamping={true}
      target={[CAMERA_CONFIG.target.x, CAMERA_CONFIG.target.y, CAMERA_CONFIG.target.z]}
    />
  );
}
