/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNMb2NhbERpc2tEJTVDJTVDVUklNUMlNUNBVUdNRU5ULUFJJTVDJTVDUE9SVEZPTElPNiU1QyU1Q21lcm9kZXYtY2xvbmUlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWtIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxMb2NhbERpc2tEXFxcXFVJXFxcXEFVR01FTlQtQUlcXFxcUE9SVEZPTElPNlxcXFxtZXJvZGV2LWNsb25lXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxMb2NhbERpc2tEXFxVSVxcQVVHTUVOVC1BSVxcUE9SVEZPTElPNlxcbWVyb2Rldi1jbG9uZVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcTG9jYWxEaXNrRFxcVUlcXEFVR01FTlQtQUlcXFBPUlRGT0xJTzZcXG1lcm9kZXYtY2xvbmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxMb2NhbERpc2tEXFxVSVxcQVVHTUVOVC1BSVxcUE9SVEZPTElPNlxcbWVyb2Rldi1jbG9uZVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNMb2NhbERpc2tEJTVDJTVDVUklNUMlNUNBVUdNRU5ULUFJJTVDJTVDUE9SVEZPTElPNiU1QyU1Q21lcm9kZXYtY2xvbmUlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWtIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxMb2NhbERpc2tEXFxcXFVJXFxcXEFVR01FTlQtQUlcXFxcUE9SVEZPTElPNlxcXFxtZXJvZGV2LWNsb25lXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _components_3d_AssetLoader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/3d/AssetLoader */ \"(ssr)/./src/components/3d/AssetLoader.tsx\");\n/* harmony import */ var _components_3d_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/3d/PerformanceOptimizer */ \"(ssr)/./src/components/3d/PerformanceOptimizer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"w-full h-screen overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.Canvas, {\n            camera: {\n                position: [\n                    10,\n                    10,\n                    10\n                ],\n                fov: 50\n            },\n            shadows: true,\n            gl: {\n                antialias: true,\n                powerPreference: 'high-performance',\n                alpha: false,\n                stencil: false\n            },\n            dpr: [\n                1,\n                2\n            ],\n            performance: {\n                min: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"color\", {\n                    attach: \"background\",\n                    args: [\n                        '#0a0a0a'\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"fog\", {\n                    attach: \"fog\",\n                    args: [\n                        '#0a0a0a',\n                        10,\n                        100\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_2__.AdaptiveQuality, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                            intensity: 0.4,\n                            color: \"#404040\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"directionalLight\", {\n                            position: [\n                                10,\n                                10,\n                                5\n                            ],\n                            intensity: 1,\n                            castShadow: true,\n                            \"shadow-mapSize-width\": 2048,\n                            \"shadow-mapSize-height\": 2048,\n                            \"shadow-camera-far\": 50,\n                            \"shadow-camera-left\": -10,\n                            \"shadow-camera-right\": 10,\n                            \"shadow-camera-top\": 10,\n                            \"shadow-camera-bottom\": -10\n                        }, void 0, false, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"spotLight\", {\n                            position: [\n                                5,\n                                8,\n                                5\n                            ],\n                            color: \"#00ffff\",\n                            intensity: 0.5,\n                            angle: Math.PI / 6,\n                            penumbra: 0.1,\n                            decay: 2,\n                            distance: 30\n                        }, void 0, false, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"spotLight\", {\n                            position: [\n                                -5,\n                                6,\n                                -5\n                            ],\n                            color: \"#ff00ff\",\n                            intensity: 0.3,\n                            angle: Math.PI / 8,\n                            penumbra: 0.2,\n                            decay: 2,\n                            distance: 25\n                        }, void 0, false, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.OrbitControls, {\n                            enablePan: true,\n                            enableZoom: true,\n                            enableRotate: true,\n                            minDistance: 5,\n                            maxDistance: 50\n                        }, void 0, false, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_AssetLoader__WEBPACK_IMPORTED_MODULE_1__.AssetLoader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_AssetLoader__WEBPACK_IMPORTED_MODULE_1__.AboutBuilding, {}, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_AssetLoader__WEBPACK_IMPORTED_MODULE_1__.ProjectsBuilding, {}, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_AssetLoader__WEBPACK_IMPORTED_MODULE_1__.ContactBuilding, {}, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_AssetLoader__WEBPACK_IMPORTED_MODULE_1__.DecorativeElements, {}, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_AssetLoader__WEBPACK_IMPORTED_MODULE_1__.BackgroundElements, {}, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_2__.InstancedDecorations, {\n                            count: 15\n                        }, void 0, false, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_2__.OptimizedGround, {}, void 0, false, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                -4,\n                                -0.8,\n                                -2\n                            ],\n                            receiveShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        3,\n                                        0.4,\n                                        3\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#2a2a2a\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                0,\n                                -0.8,\n                                0\n                            ],\n                            receiveShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        4,\n                                        0.4,\n                                        4\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#2a2a2a\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                4,\n                                -0.8,\n                                2\n                            ],\n                            receiveShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        3,\n                                        0.4,\n                                        3\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#2a2a2a\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                -2,\n                                -0.9,\n                                -1\n                            ],\n                            receiveShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        2,\n                                        0.2,\n                                        0.5\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#333333\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                2,\n                                -0.9,\n                                1\n                            ],\n                            receiveShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        2,\n                                        0.2,\n                                        0.5\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#333333\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                -8,\n                                0.5,\n                                -8\n                            ],\n                            castShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        1,\n                                        1,\n                                        1\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#0a0a0a\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                8,\n                                0.5,\n                                8\n                            ],\n                            castShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        1,\n                                        1,\n                                        1\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#0a0a0a\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                -8,\n                                0.5,\n                                8\n                            ],\n                            castShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        1,\n                                        1,\n                                        1\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#0a0a0a\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                            position: [\n                                8,\n                                0.5,\n                                -8\n                            ],\n                            castShadow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                                    args: [\n                                        1,\n                                        1,\n                                        1\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                    color: \"#0a0a0a\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_3d_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_2__.PerformanceStats, {}, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/3d/AssetLoader.tsx":
/*!*******************************************!*\
  !*** ./src/components/3d/AssetLoader.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutBuilding: () => (/* binding */ AboutBuilding),\n/* harmony export */   AssetLoader: () => (/* binding */ AssetLoader),\n/* harmony export */   BackgroundElements: () => (/* binding */ BackgroundElements),\n/* harmony export */   ContactBuilding: () => (/* binding */ ContactBuilding),\n/* harmony export */   DecorativeElements: () => (/* binding */ DecorativeElements),\n/* harmony export */   ProjectsBuilding: () => (/* binding */ ProjectsBuilding),\n/* harmony export */   useAssetLoadingProgress: () => (/* binding */ useAssetLoadingProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-f681e724.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/constants */ \"(ssr)/./src/utils/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ AssetLoader,AboutBuilding,ProjectsBuilding,ContactBuilding,DecorativeElements,BackgroundElements,useAssetLoadingProgress auto */ \n\n// import { useGLTF } from '@react-three/drei'; // Will be used when we have actual GLTF models\n\n\nfunction BuildingModel({ position, rotation = [\n    0,\n    0,\n    0\n], scale = [\n    1,\n    1,\n    1\n], color = '#ffffff', emissive = '#000000', fallbackGeometry = 'box', fallbackSize = [\n    2,\n    2,\n    2\n], animate = false }) {\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Add subtle animation\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C)({\n        \"BuildingModel.useFrame\": (state)=>{\n            if (meshRef.current && animate) {\n                meshRef.current.rotation.y = rotation[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n                meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.8) * 0.05;\n            }\n        }\n    }[\"BuildingModel.useFrame\"]);\n    // For now, we'll use fallback geometry since we don't have actual models\n    // In the future, this would load the GLTF model:\n    // const { scene } = useGLTF(modelPath);\n    const renderFallbackGeometry = ()=>{\n        switch(fallbackGeometry){\n            case 'cylinder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cylinderGeometry\", {\n                    args: fallbackSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n            case 'sphere':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"sphereGeometry\", {\n                    args: [\n                        fallbackSize[0]\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                    args: fallbackSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        ref: meshRef,\n        position: position,\n        rotation: rotation,\n        scale: scale,\n        castShadow: true,\n        children: [\n            renderFallbackGeometry(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                color: color,\n                emissive: emissive,\n                roughness: 0.7,\n                metalness: 0.3\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n// Loading fallback component\nfunction LoadingFallback() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                args: [\n                    0.5,\n                    0.5,\n                    0.5\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshBasicMaterial\", {\n                color: \"#333333\",\n                wireframe: true\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\nfunction AssetLoader({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n            lineNumber: 109,\n            columnNumber: 25\n        }, void 0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n// Specific building components using enhanced positioning\nfunction AboutBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.about;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#00ffff\",\n        emissive: \"#001a1a\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            1.5,\n            2,\n            1.5\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\nfunction ProjectsBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.projects;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.projectsBuilding,\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#ff00ff\",\n        emissive: \"#1a001a\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            2,\n            3,\n            2\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\nfunction ContactBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.contact;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.contactBuilding,\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#ffff00\",\n        emissive: \"#1a1a00\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            1.5,\n            2,\n            1.5\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\nfunction DecorativeElements() {\n    const decorative1 = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.decorative1;\n    const decorative2 = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.decorative2;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    decorative1.x,\n                    decorative1.y,\n                    decorative1.z\n                ],\n                rotation: [\n                    decorative1.rotation.x,\n                    decorative1.rotation.y,\n                    decorative1.rotation.z\n                ],\n                scale: [\n                    decorative1.scale.x,\n                    decorative1.scale.y,\n                    decorative1.scale.z\n                ],\n                color: \"#ffffff\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"cylinder\",\n                fallbackSize: [\n                    0.3,\n                    1,\n                    0.3\n                ],\n                animate: false\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    decorative2.x,\n                    decorative2.y,\n                    decorative2.z\n                ],\n                rotation: [\n                    decorative2.rotation.x,\n                    decorative2.rotation.y,\n                    decorative2.rotation.z\n                ],\n                scale: [\n                    decorative2.scale.x,\n                    decorative2.scale.y,\n                    decorative2.scale.z\n                ],\n                color: \"#ffffff\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"cylinder\",\n                fallbackSize: [\n                    0.3,\n                    1,\n                    0.3\n                ],\n                animate: false\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction BackgroundElements() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: Object.entries(_utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS).filter(([key])=>key.startsWith('background')).map(([key, config])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    config.x,\n                    config.y,\n                    config.z\n                ],\n                rotation: [\n                    config.rotation.x,\n                    config.rotation.y,\n                    config.rotation.z\n                ],\n                scale: [\n                    config.scale.x,\n                    config.scale.y,\n                    config.scale.z\n                ],\n                color: \"#0a0a0a\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"box\",\n                fallbackSize: [\n                    1,\n                    1,\n                    1\n                ],\n                animate: false\n            }, key, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 204,\n                columnNumber: 11\n            }, this))\n    }, void 0, false);\n}\n// Hook for asset loading progress (future implementation)\nfunction useAssetLoadingProgress() {\n    // This would track loading progress of all assets\n    // For now, return mock data\n    return {\n        progress: 100,\n        isLoading: false,\n        error: null,\n        loadedAssets: [\n            'placeholder'\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/3d/AssetLoader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/3d/PerformanceOptimizer.tsx":
/*!****************************************************!*\
  !*** ./src/components/3d/PerformanceOptimizer.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdaptiveQuality: () => (/* binding */ AdaptiveQuality),\n/* harmony export */   FrustumCulling: () => (/* binding */ FrustumCulling),\n/* harmony export */   InstancedDecorations: () => (/* binding */ InstancedDecorations),\n/* harmony export */   LODBuilding: () => (/* binding */ LODBuilding),\n/* harmony export */   OptimizedGround: () => (/* binding */ OptimizedGround),\n/* harmony export */   PerformanceStats: () => (/* binding */ PerformanceStats),\n/* harmony export */   useMemoryManagement: () => (/* binding */ useMemoryManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-f681e724.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/Detailed.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/PerformanceMonitor.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/constants */ \"(ssr)/./src/utils/constants.ts\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* __next_internal_client_entry_do_not_use__ LODBuilding,InstancedDecorations,AdaptiveQuality,FrustumCulling,OptimizedGround,useMemoryManagement,PerformanceStats auto */ \n\n\n\n\n\n// Level of Detail component for buildings\nfunction LODBuilding({ position, rotation = [\n    0,\n    0,\n    0\n], scale = [\n    1,\n    1,\n    1\n], color, emissive = '#000000' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Detailed, {\n        distances: [\n            0,\n            15,\n            25,\n            35\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                position: position,\n                rotation: rotation,\n                scale: scale,\n                castShadow: true,\n                receiveShadow: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                        args: [\n                            2,\n                            3,\n                            2\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                        color: color,\n                        emissive: emissive,\n                        roughness: 0.7,\n                        metalness: 0.3\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                position: position,\n                rotation: rotation,\n                scale: scale,\n                castShadow: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                        args: [\n                            2,\n                            3,\n                            2\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshLambertMaterial\", {\n                        color: color,\n                        emissive: emissive\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                position: position,\n                rotation: rotation,\n                scale: scale,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                        args: [\n                            2,\n                            3,\n                            2\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshBasicMaterial\", {\n                        color: color\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                position: position,\n                rotation: rotation,\n                scale: scale,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                        args: [\n                            1,\n                            2,\n                            1\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshBasicMaterial\", {\n                        color: color\n                    }, void 0, false, {\n                        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n// Instanced decorative elements for better performance\nfunction InstancedDecorations({ count = 20 }) {\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const positions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"InstancedDecorations.useMemo[positions]\": ()=>{\n            const temp = [];\n            for(let i = 0; i < count; i++){\n                temp.push([\n                    (Math.random() - 0.5) * 30,\n                    0.2,\n                    (Math.random() - 0.5) * 30\n                ]);\n            }\n            return temp;\n        }\n    }[\"InstancedDecorations.useMemo[positions]\"], [\n        count\n    ]);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.C)({\n        \"InstancedDecorations.useFrame\": (state)=>{\n            if (meshRef.current) {\n                const time = state.clock.elapsedTime;\n                for(let i = 0; i < count; i++){\n                    const matrix = new three__WEBPACK_IMPORTED_MODULE_5__.Matrix4();\n                    const [x, y, z] = positions[i];\n                    matrix.setPosition(x, y + Math.sin(time + i) * 0.1, z);\n                    meshRef.current.setMatrixAt(i, matrix);\n                }\n                meshRef.current.instanceMatrix.needsUpdate = true;\n            }\n        }\n    }[\"InstancedDecorations.useFrame\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"instancedMesh\", {\n        ref: meshRef,\n        args: [\n            undefined,\n            undefined,\n            count\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cylinderGeometry\", {\n                args: [\n                    0.1,\n                    0.1,\n                    0.4\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshBasicMaterial\", {\n                color: \"#444444\"\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\nfunction AdaptiveQuality({ children }) {\n    const { gl, scene } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.A)();\n    const qualityRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(1);\n    const handlePerformanceChange = (api)=>{\n        const fps = api.fps;\n        if (fps < _utils_constants__WEBPACK_IMPORTED_MODULE_2__.PERFORMANCE_THRESHOLDS.minFPS) {\n            // Reduce quality\n            qualityRef.current = Math.max(0.5, qualityRef.current - 0.1);\n            gl.setPixelRatio(Math.min(window.devicePixelRatio, qualityRef.current));\n            // Disable shadows if performance is very poor\n            if (fps < 20) {\n                scene.traverse((object)=>{\n                    if (object instanceof three__WEBPACK_IMPORTED_MODULE_5__.Mesh) {\n                        object.castShadow = false;\n                        object.receiveShadow = false;\n                    }\n                });\n            }\n        } else if (fps > _utils_constants__WEBPACK_IMPORTED_MODULE_2__.PERFORMANCE_THRESHOLDS.targetFPS * 0.9) {\n            // Increase quality\n            qualityRef.current = Math.min(1, qualityRef.current + 0.05);\n            gl.setPixelRatio(Math.min(window.devicePixelRatio, qualityRef.current));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_6__.PerformanceMonitor, {\n                onIncline: handlePerformanceChange,\n                onDecline: handlePerformanceChange\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true);\n}\n// Frustum culling helper\nfunction FrustumCulling({ children }) {\n    const { camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.A)();\n    const frustum = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FrustumCulling.useMemo[frustum]\": ()=>new three__WEBPACK_IMPORTED_MODULE_5__.Frustum()\n    }[\"FrustumCulling.useMemo[frustum]\"], []);\n    const cameraMatrix = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FrustumCulling.useMemo[cameraMatrix]\": ()=>new three__WEBPACK_IMPORTED_MODULE_5__.Matrix4()\n    }[\"FrustumCulling.useMemo[cameraMatrix]\"], []);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.C)({\n        \"FrustumCulling.useFrame\": ()=>{\n            cameraMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);\n            frustum.setFromProjectionMatrix(cameraMatrix);\n        }\n    }[\"FrustumCulling.useFrame\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Optimized ground with reduced geometry\nfunction OptimizedGround() {\n    const groundRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use lower resolution geometry for better performance\n    const geometry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"OptimizedGround.useMemo[geometry]\": ()=>{\n            return new three__WEBPACK_IMPORTED_MODULE_5__.PlaneGeometry(20, 20, 4, 4);\n        }\n    }[\"OptimizedGround.useMemo[geometry]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        ref: groundRef,\n        rotation: [\n            -Math.PI / 2,\n            0,\n            0\n        ],\n        position: [\n            0,\n            -1,\n            0\n        ],\n        receiveShadow: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                object: geometry\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshLambertMaterial\", {\n                color: \"#1a1a1a\"\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n// Memory management utilities\nfunction useMemoryManagement() {\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.A)();\n    const cleanupMemory = ()=>{\n        // Force garbage collection of GPU resources\n        gl.dispose();\n        // Clear any cached geometries and materials\n        three__WEBPACK_IMPORTED_MODULE_5__.Cache.clear();\n    };\n    return {\n        cleanupMemory\n    };\n}\n// Performance stats display\nfunction PerformanceStats() {\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.A)();\n    const statsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.C)({\n        \"PerformanceStats.useFrame\": ()=>{\n            if (statsRef.current) {\n                const info = gl.info;\n                statsRef.current.innerHTML = `\n        <div style=\"position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; font-family: monospace; font-size: 12px; z-index: 1000;\">\n          <div>Triangles: ${info.render.triangles}</div>\n          <div>Draw Calls: ${info.render.calls}</div>\n          <div>Geometries: ${info.memory.geometries}</div>\n          <div>Textures: ${info.memory.textures}</div>\n        </div>\n      `;\n            }\n        }\n    }[\"PerformanceStats.useFrame\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: statsRef\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 214,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/3d/PerformanceOptimizer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/constants.ts":
/*!********************************!*\
  !*** ./src/utils/constants.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ANIMATION_DURATIONS: () => (/* binding */ ANIMATION_DURATIONS),\n/* harmony export */   ASSET_PATHS: () => (/* binding */ ASSET_PATHS),\n/* harmony export */   BUILDING_POSITIONS: () => (/* binding */ BUILDING_POSITIONS),\n/* harmony export */   CAMERA_CONFIG: () => (/* binding */ CAMERA_CONFIG),\n/* harmony export */   COLORS: () => (/* binding */ COLORS),\n/* harmony export */   LIGHT_CONFIG: () => (/* binding */ LIGHT_CONFIG),\n/* harmony export */   PERFORMANCE_THRESHOLDS: () => (/* binding */ PERFORMANCE_THRESHOLDS),\n/* harmony export */   RESPONSIVE_CONFIG: () => (/* binding */ RESPONSIVE_CONFIG),\n/* harmony export */   SCENE_CONFIG: () => (/* binding */ SCENE_CONFIG),\n/* harmony export */   SHADER_CONFIG: () => (/* binding */ SHADER_CONFIG),\n/* harmony export */   UI_CONFIG: () => (/* binding */ UI_CONFIG),\n/* harmony export */   Z_INDEX: () => (/* binding */ Z_INDEX)\n/* harmony export */ });\n// Scene configuration\nconst SCENE_CONFIG = {\n    background: '#0a0a0a',\n    fog: {\n        color: '#0a0a0a',\n        near: 10,\n        far: 100\n    },\n    shadows: {\n        enabled: true,\n        type: 'PCFSoftShadowMap'\n    }\n};\n// Isometric camera configuration\nconst CAMERA_CONFIG = {\n    position: {\n        x: 10,\n        y: 10,\n        z: 10\n    },\n    target: {\n        x: 0,\n        y: 0,\n        z: 0\n    },\n    fov: 50,\n    near: 0.1,\n    far: 1000,\n    enableControls: true,\n    autoRotate: false\n};\n// Lighting setup\nconst LIGHT_CONFIG = {\n    ambient: {\n        color: '#404040',\n        intensity: 0.4\n    },\n    directional: {\n        color: '#ffffff',\n        intensity: 1,\n        position: {\n            x: 10,\n            y: 10,\n            z: 5\n        },\n        castShadow: true\n    },\n    spot: [\n        {\n            color: '#00ffff',\n            intensity: 0.5,\n            position: {\n                x: 5,\n                y: 8,\n                z: 5\n            },\n            target: {\n                x: 0,\n                y: 0,\n                z: 0\n            },\n            angle: Math.PI / 6,\n            penumbra: 0.1\n        },\n        {\n            color: '#ff00ff',\n            intensity: 0.3,\n            position: {\n                x: -5,\n                y: 6,\n                z: -5\n            },\n            target: {\n                x: 0,\n                y: 0,\n                z: 0\n            },\n            angle: Math.PI / 8,\n            penumbra: 0.2\n        }\n    ]\n};\n// Enhanced building positions for the micro-city layout\nconst BUILDING_POSITIONS = {\n    about: {\n        x: -4,\n        y: 1,\n        z: -2,\n        rotation: {\n            x: 0,\n            y: 0.2,\n            z: 0\n        },\n        scale: {\n            x: 1,\n            y: 1,\n            z: 1\n        }\n    },\n    projects: {\n        x: 0,\n        y: 1.5,\n        z: 0,\n        rotation: {\n            x: 0,\n            y: 0,\n            z: 0\n        },\n        scale: {\n            x: 1.1,\n            y: 1.2,\n            z: 1.1\n        }\n    },\n    contact: {\n        x: 4,\n        y: 1,\n        z: 2,\n        rotation: {\n            x: 0,\n            y: -0.2,\n            z: 0\n        },\n        scale: {\n            x: 1,\n            y: 1,\n            z: 1\n        }\n    },\n    decorative1: {\n        x: -2,\n        y: 0.5,\n        z: 4,\n        rotation: {\n            x: 0,\n            y: 0,\n            z: 0\n        },\n        scale: {\n            x: 1,\n            y: 1,\n            z: 1\n        }\n    },\n    decorative2: {\n        x: 2,\n        y: 0.5,\n        z: -4,\n        rotation: {\n            x: 0,\n            y: 0,\n            z: 0\n        },\n        scale: {\n            x: 1,\n            y: 1,\n            z: 1\n        }\n    },\n    // Additional background elements\n    background1: {\n        x: -6,\n        y: 0.5,\n        z: -6,\n        rotation: {\n            x: 0,\n            y: 0.5,\n            z: 0\n        },\n        scale: {\n            x: 0.8,\n            y: 0.8,\n            z: 0.8\n        }\n    },\n    background2: {\n        x: 6,\n        y: 0.5,\n        z: 6,\n        rotation: {\n            x: 0,\n            y: -0.5,\n            z: 0\n        },\n        scale: {\n            x: 0.8,\n            y: 0.8,\n            z: 0.8\n        }\n    },\n    background3: {\n        x: -6,\n        y: 0.5,\n        z: 6,\n        rotation: {\n            x: 0,\n            y: 0.3,\n            z: 0\n        },\n        scale: {\n            x: 0.8,\n            y: 0.8,\n            z: 0.8\n        }\n    },\n    background4: {\n        x: 6,\n        y: 0.5,\n        z: -6,\n        rotation: {\n            x: 0,\n            y: -0.3,\n            z: 0\n        },\n        scale: {\n            x: 0.8,\n            y: 0.8,\n            z: 0.8\n        }\n    }\n};\n// Responsive breakpoints\nconst RESPONSIVE_CONFIG = {\n    mobile: {\n        maxWidth: 768,\n        camera: {\n            ...CAMERA_CONFIG,\n            position: {\n                x: 15,\n                y: 15,\n                z: 15\n            },\n            fov: 60\n        },\n        ui: {\n            scale: 0.8,\n            touchEnabled: true\n        }\n    },\n    tablet: {\n        maxWidth: 1024,\n        camera: {\n            ...CAMERA_CONFIG,\n            position: {\n                x: 12,\n                y: 12,\n                z: 12\n            },\n            fov: 55\n        },\n        ui: {\n            scale: 0.9,\n            touchEnabled: true\n        }\n    },\n    desktop: {\n        minWidth: 1025,\n        camera: CAMERA_CONFIG,\n        ui: {\n            scale: 1,\n            touchEnabled: false\n        }\n    }\n};\n// Animation durations\nconst ANIMATION_DURATIONS = {\n    hover: 0.3,\n    click: 0.5,\n    navigation: 1.2,\n    sceneTransition: 2.0,\n    glow: 2.0\n};\n// Performance thresholds\nconst PERFORMANCE_THRESHOLDS = {\n    targetFPS: 60,\n    minFPS: 30,\n    maxMemoryMB: 512,\n    maxDrawCalls: 100,\n    maxTriangles: 100000\n};\n// Asset paths\nconst ASSET_PATHS = {\n    models: {\n        aboutBuilding: '/models/about-building.glb',\n        projectsBuilding: '/models/projects-building.glb',\n        contactBuilding: '/models/contact-building.glb',\n        environment: '/models/environment.glb',\n        decorative: '/models/decorative.glb'\n    },\n    textures: {\n        ground: '/textures/ground.jpg',\n        sky: '/textures/sky.jpg',\n        glow: '/textures/glow.png'\n    }\n};\n// Shader configuration\nconst SHADER_CONFIG = {\n    glow: {\n        vertexShader: `\n      varying vec2 vUv;\n      varying vec3 vNormal;\n      varying vec3 vPosition;\n      \n      void main() {\n        vUv = uv;\n        vNormal = normalize(normalMatrix * normal);\n        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n      }\n    `,\n        fragmentShader: `\n      uniform float time;\n      uniform vec3 glowColor;\n      uniform float glowIntensity;\n      uniform float emissiveStrength;\n      \n      varying vec2 vUv;\n      varying vec3 vNormal;\n      varying vec3 vPosition;\n      \n      void main() {\n        float fresnel = pow(1.0 - dot(vNormal, normalize(-vPosition)), 2.0);\n        float pulse = sin(time * 2.0) * 0.5 + 0.5;\n        vec3 glow = glowColor * glowIntensity * fresnel * (0.5 + pulse * 0.5);\n        \n        gl_FragColor = vec4(glow * emissiveStrength, 1.0);\n      }\n    `\n    }\n};\n// UI configuration\nconst UI_CONFIG = {\n    overlay: {\n        fadeInDuration: 0.5,\n        fadeOutDuration: 0.3,\n        maxWidth: '600px',\n        padding: '2rem'\n    },\n    navigation: {\n        transitionDuration: 0.8,\n        easing: 'power2.inOut'\n    },\n    form: {\n        validationDelay: 300,\n        submitTimeout: 5000\n    }\n};\n// Color palette\nconst COLORS = {\n    primary: '#00ffff',\n    secondary: '#ff00ff',\n    accent: '#ffff00',\n    background: '#0a0a0a',\n    surface: '#1a1a1a',\n    text: '#ffffff',\n    textSecondary: '#cccccc',\n    error: '#ff4444',\n    success: '#44ff44',\n    warning: '#ffaa44'\n};\n// Z-index layers\nconst Z_INDEX = {\n    background: 0,\n    scene: 1,\n    overlay: 10,\n    modal: 20,\n    tooltip: 30,\n    loading: 40\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/constants.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@react-three","vendor-chunks/react-reconciler","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/three","vendor-chunks/three-stdlib","vendor-chunks/react-use-measure","vendor-chunks/its-fine","vendor-chunks/@babel","vendor-chunks/suspend-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();