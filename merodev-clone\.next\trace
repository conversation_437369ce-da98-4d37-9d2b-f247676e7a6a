[{"name": "generate-buildid", "duration": 287, "timestamp": 83198225381, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751513347722, "traceId": "8ccd8366d4b03a68"}, {"name": "load-custom-routes", "duration": 368, "timestamp": 83198225775, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751513347723, "traceId": "8ccd8366d4b03a68"}, {"name": "create-dist-dir", "duration": 857, "timestamp": 83198334407, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751513347831, "traceId": "8ccd8366d4b03a68"}, {"name": "create-pages-mapping", "duration": 453, "timestamp": 83198370987, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751513347868, "traceId": "8ccd8366d4b03a68"}, {"name": "collect-app-paths", "duration": 3959, "timestamp": 83198371531, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751513347869, "traceId": "8ccd8366d4b03a68"}, {"name": "create-app-mapping", "duration": 3300, "timestamp": 83198375561, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751513347873, "traceId": "8ccd8366d4b03a68"}, {"name": "public-dir-conflict-check", "duration": 1392, "timestamp": 83198379678, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751513347877, "traceId": "8ccd8366d4b03a68"}, {"name": "generate-routes-manifest", "duration": 3469, "timestamp": 83198381599, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751513347879, "traceId": "8ccd8366d4b03a68"}, {"name": "create-entrypoints", "duration": 25120, "timestamp": 83200045746, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751513349542, "traceId": "8ccd8366d4b03a68"}, {"name": "generate-webpack-config", "duration": 577664, "timestamp": 83200071237, "id": 16, "parentId": 14, "tags": {}, "startTime": 1751513349568, "traceId": "8ccd8366d4b03a68"}, {"name": "next-trace-entrypoint-plugin", "duration": 2588, "timestamp": 83200847346, "id": 18, "parentId": 17, "tags": {}, "startTime": 1751513350344, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 447999, "timestamp": 83200858869, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751513350355, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 495038, "timestamp": 83200858904, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751513350355, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 546675, "timestamp": 83200858950, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751513350355, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 626101, "timestamp": 83200858062, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751513350355, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 627343, "timestamp": 83200858688, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751513350355, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 628389, "timestamp": 83200858928, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CLocalDiskD%5CUI%5CAUGMENT-AI%5CPORTFOLIO6%5Cmerodev-clone%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751513350355, "traceId": "8ccd8366d4b03a68"}, {"name": "build-module-tsx", "duration": 63988, "timestamp": 83201917388, "id": 36, "parentId": 17, "tags": {"name": "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\AssetLoader.tsx", "layer": "ssr"}, "startTime": 1751513351414, "traceId": "8ccd8366d4b03a68"}, {"name": "build-module-tsx", "duration": 64828, "timestamp": 83201927693, "id": 37, "parentId": 17, "tags": {"name": "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\PerformanceOptimizer.tsx", "layer": "ssr"}, "startTime": 1751513351424, "traceId": "8ccd8366d4b03a68"}, {"name": "make", "duration": 1516406, "timestamp": 83200857440, "id": 19, "parentId": 17, "tags": {}, "startTime": 1751513350354, "traceId": "8ccd8366d4b03a68"}, {"name": "get-entries", "duration": 1375, "timestamp": 83202376273, "id": 39, "parentId": 38, "tags": {}, "startTime": 1751513351873, "traceId": "8ccd8366d4b03a68"}, {"name": "node-file-trace-plugin", "duration": 82987, "timestamp": 83202381561, "id": 40, "parentId": 38, "tags": {"traceEntryCount": "8"}, "startTime": 1751513351878, "traceId": "8ccd8366d4b03a68"}, {"name": "collect-traced-files", "duration": 809, "timestamp": 83202464574, "id": 41, "parentId": 38, "tags": {}, "startTime": 1751513351961, "traceId": "8ccd8366d4b03a68"}, {"name": "finish-modules", "duration": 89390, "timestamp": 83202376010, "id": 38, "parentId": 18, "tags": {}, "startTime": 1751513351873, "traceId": "8ccd8366d4b03a68"}, {"name": "chunk-graph", "duration": 12738, "timestamp": 83202586608, "id": 43, "parentId": 42, "tags": {}, "startTime": 1751513352083, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-modules", "duration": 25, "timestamp": 83202599498, "id": 45, "parentId": 42, "tags": {}, "startTime": 1751513352096, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-chunks", "duration": 19533, "timestamp": 83202599605, "id": 46, "parentId": 42, "tags": {}, "startTime": 1751513352096, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-tree", "duration": 204, "timestamp": 83202619286, "id": 47, "parentId": 42, "tags": {}, "startTime": 1751513352116, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-chunk-modules", "duration": 16651, "timestamp": 83202619625, "id": 48, "parentId": 42, "tags": {}, "startTime": 1751513352116, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize", "duration": 36966, "timestamp": 83202599427, "id": 44, "parentId": 42, "tags": {}, "startTime": 1751513352096, "traceId": "8ccd8366d4b03a68"}, {"name": "module-hash", "duration": 20995, "timestamp": 83202661070, "id": 49, "parentId": 42, "tags": {}, "startTime": 1751513352158, "traceId": "8ccd8366d4b03a68"}, {"name": "code-generation", "duration": 552817, "timestamp": 83202682155, "id": 50, "parentId": 42, "tags": {}, "startTime": 1751513352179, "traceId": "8ccd8366d4b03a68"}, {"name": "hash", "duration": 8284, "timestamp": 83203240439, "id": 51, "parentId": 42, "tags": {}, "startTime": 1751513352737, "traceId": "8ccd8366d4b03a68"}, {"name": "code-generation-jobs", "duration": 283, "timestamp": 83203248720, "id": 52, "parentId": 42, "tags": {}, "startTime": 1751513352745, "traceId": "8ccd8366d4b03a68"}, {"name": "module-assets", "duration": 368, "timestamp": 83203248958, "id": 53, "parentId": 42, "tags": {}, "startTime": 1751513352745, "traceId": "8ccd8366d4b03a68"}, {"name": "create-chunk-assets", "duration": 5163, "timestamp": 83203249392, "id": 54, "parentId": 42, "tags": {}, "startTime": 1751513352746, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 43068, "timestamp": 83203273541, "id": 56, "parentId": 55, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751513352770, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 38061, "timestamp": 83203278562, "id": 58, "parentId": 55, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751513352775, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 38018, "timestamp": 83203278607, "id": 59, "parentId": 55, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751513352775, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 3260, "timestamp": 83203313366, "id": 61, "parentId": 55, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751513352810, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 3192, "timestamp": 83203313436, "id": 62, "parentId": 55, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751513352810, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 3177, "timestamp": 83203313455, "id": 63, "parentId": 55, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1751513352810, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 76, "timestamp": 83203316558, "id": 65, "parentId": 55, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1751513352813, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 105788, "timestamp": 83203273962, "id": 57, "parentId": 55, "tags": {"name": "../app/favicon.ico/route.js", "cache": "MISS"}, "startTime": 1751513352770, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 198135, "timestamp": 83203313474, "id": 64, "parentId": 55, "tags": {"name": "145.js", "cache": "MISS"}, "startTime": 1751513352810, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 702888, "timestamp": 83203278619, "id": 60, "parentId": 55, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1751513352775, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-webpack-plugin-optimize", "duration": 717849, "timestamp": 83203263684, "id": 55, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751513352760, "traceId": "8ccd8366d4b03a68"}, {"name": "css-minimizer-plugin", "duration": 198, "timestamp": 83203981771, "id": 66, "parentId": 17, "tags": {}, "startTime": 1751513353478, "traceId": "8ccd8366d4b03a68"}, {"name": "create-trace-assets", "duration": 1513, "timestamp": 83203982200, "id": 67, "parentId": 18, "tags": {}, "startTime": 1751513353479, "traceId": "8ccd8366d4b03a68"}, {"name": "seal", "duration": 1464472, "timestamp": 83202527682, "id": 42, "parentId": 17, "tags": {}, "startTime": 1751513352024, "traceId": "8ccd8366d4b03a68"}, {"name": "webpack-compilation", "duration": 3152516, "timestamp": 83200844690, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1751513350341, "traceId": "8ccd8366d4b03a68"}, {"name": "emit", "duration": 32315, "timestamp": 83203997797, "id": 68, "parentId": 14, "tags": {}, "startTime": 1751513353494, "traceId": "8ccd8366d4b03a68"}, {"name": "webpack-close", "duration": 201671, "timestamp": 83204032641, "id": 69, "parentId": 14, "tags": {"name": "server"}, "startTime": 1751513353529, "traceId": "8ccd8366d4b03a68"}, {"name": "webpack-generate-error-stats", "duration": 2739, "timestamp": 83204234397, "id": 70, "parentId": 69, "tags": {}, "startTime": 1751513353731, "traceId": "8ccd8366d4b03a68"}, {"name": "run-webpack-compiler", "duration": 4191738, "timestamp": 83200045726, "id": 14, "parentId": 13, "tags": {}, "startTime": 1751513349542, "traceId": "8ccd8366d4b03a68"}, {"name": "format-webpack-messages", "duration": 234, "timestamp": 83204237473, "id": 71, "parentId": 13, "tags": {}, "startTime": 1751513353734, "traceId": "8ccd8366d4b03a68"}, {"name": "worker-main-server", "duration": 4192941, "timestamp": 83200044932, "id": 13, "parentId": 1, "tags": {}, "startTime": 1751513349541, "traceId": "8ccd8366d4b03a68"}, {"name": "create-entrypoints", "duration": 22579, "timestamp": 83205336432, "id": 75, "parentId": 73, "tags": {}, "startTime": 1751513354833, "traceId": "8ccd8366d4b03a68"}, {"name": "generate-webpack-config", "duration": 429483, "timestamp": 83205359189, "id": 76, "parentId": 74, "tags": {}, "startTime": 1751513354856, "traceId": "8ccd8366d4b03a68"}, {"name": "make", "duration": 817, "timestamp": 83205936003, "id": 78, "parentId": 77, "tags": {}, "startTime": 1751513355433, "traceId": "8ccd8366d4b03a68"}, {"name": "chunk-graph", "duration": 681, "timestamp": 83205940049, "id": 80, "parentId": 79, "tags": {}, "startTime": 1751513355437, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-modules", "duration": 21, "timestamp": 83205940844, "id": 82, "parentId": 79, "tags": {}, "startTime": 1751513355438, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-chunks", "duration": 1188, "timestamp": 83205940950, "id": 83, "parentId": 79, "tags": {}, "startTime": 1751513355438, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-tree", "duration": 146, "timestamp": 83205942229, "id": 84, "parentId": 79, "tags": {}, "startTime": 1751513355439, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize-chunk-modules", "duration": 610, "timestamp": 83205942626, "id": 85, "parentId": 79, "tags": {}, "startTime": 1751513355439, "traceId": "8ccd8366d4b03a68"}, {"name": "optimize", "duration": 2528, "timestamp": 83205940793, "id": 81, "parentId": 79, "tags": {}, "startTime": 1751513355438, "traceId": "8ccd8366d4b03a68"}, {"name": "module-hash", "duration": 90, "timestamp": 83205944345, "id": 86, "parentId": 79, "tags": {}, "startTime": 1751513355441, "traceId": "8ccd8366d4b03a68"}, {"name": "code-generation", "duration": 206, "timestamp": 83205944471, "id": 87, "parentId": 79, "tags": {}, "startTime": 1751513355441, "traceId": "8ccd8366d4b03a68"}, {"name": "hash", "duration": 425, "timestamp": 83205944920, "id": 88, "parentId": 79, "tags": {}, "startTime": 1751513355442, "traceId": "8ccd8366d4b03a68"}, {"name": "code-generation-jobs", "duration": 115, "timestamp": 83205945343, "id": 89, "parentId": 79, "tags": {}, "startTime": 1751513355442, "traceId": "8ccd8366d4b03a68"}, {"name": "module-assets", "duration": 80, "timestamp": 83205945433, "id": 90, "parentId": 79, "tags": {}, "startTime": 1751513355442, "traceId": "8ccd8366d4b03a68"}, {"name": "create-chunk-assets", "duration": 198, "timestamp": 83205945520, "id": 91, "parentId": 79, "tags": {}, "startTime": 1751513355442, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-js", "duration": 188, "timestamp": 83205960784, "id": 93, "parentId": 92, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751513355458, "traceId": "8ccd8366d4b03a68"}, {"name": "minify-webpack-plugin-optimize", "duration": 3673, "timestamp": 83205957313, "id": 92, "parentId": 77, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751513355454, "traceId": "8ccd8366d4b03a68"}, {"name": "css-minimizer-plugin", "duration": 143, "timestamp": 83205961088, "id": 94, "parentId": 77, "tags": {}, "startTime": 1751513355458, "traceId": "8ccd8366d4b03a68"}, {"name": "seal", "duration": 25784, "timestamp": 83205939321, "id": 79, "parentId": 77, "tags": {}, "startTime": 1751513355436, "traceId": "8ccd8366d4b03a68"}, {"name": "webpack-compilation", "duration": 37198, "timestamp": 83205928246, "id": 77, "parentId": 74, "tags": {"name": "edge-server"}, "startTime": 1751513355425, "traceId": "8ccd8366d4b03a68"}, {"name": "emit", "duration": 3732, "timestamp": 83205965826, "id": 95, "parentId": 74, "tags": {}, "startTime": 1751513355463, "traceId": "8ccd8366d4b03a68"}, {"name": "webpack-close", "duration": 1079, "timestamp": 83205970996, "id": 96, "parentId": 74, "tags": {"name": "edge-server"}, "startTime": 1751513355468, "traceId": "8ccd8366d4b03a68"}, {"name": "webpack-generate-error-stats", "duration": 3566, "timestamp": 83205972178, "id": 97, "parentId": 96, "tags": {}, "startTime": 1751513355469, "traceId": "8ccd8366d4b03a68"}, {"name": "run-webpack-compiler", "duration": 639466, "timestamp": 83205336418, "id": 74, "parentId": 73, "tags": {}, "startTime": 1751513354833, "traceId": "8ccd8366d4b03a68"}, {"name": "format-webpack-messages", "duration": 74, "timestamp": 83205975890, "id": 98, "parentId": 73, "tags": {}, "startTime": 1751513355473, "traceId": "8ccd8366d4b03a68"}, {"name": "worker-main-edge-server", "duration": 640431, "timestamp": 83205335656, "id": 73, "parentId": 1, "tags": {}, "startTime": 1751513354832, "traceId": "8ccd8366d4b03a68"}, {"name": "create-entrypoints", "duration": 17173, "timestamp": 83206895791, "id": 101, "parentId": 99, "tags": {}, "startTime": 1751513356392, "traceId": "8ccd8366d4b03a68"}, {"name": "generate-webpack-config", "duration": 356131, "timestamp": 83206913120, "id": 102, "parentId": 100, "tags": {}, "startTime": 1751513356410, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 316898, "timestamp": 83207395527, "id": 110, "parentId": 104, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 356480, "timestamp": 83207395415, "id": 108, "parentId": 104, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 356380, "timestamp": 83207395539, "id": 111, "parentId": 104, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 368418, "timestamp": 83207395392, "id": 107, "parentId": 104, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 370156, "timestamp": 83207395505, "id": 109, "parentId": 104, "tags": {"request": "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 371200, "timestamp": 83207395356, "id": 106, "parentId": 104, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 416756, "timestamp": 83207394981, "id": 105, "parentId": 104, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}, {"name": "postcss-process", "duration": 130953, "timestamp": 83208107165, "id": 117, "parentId": 116, "tags": {}, "startTime": 1751513357604, "traceId": "8ccd8366d4b03a68"}, {"name": "postcss-loader", "duration": 479049, "timestamp": 83207759282, "id": 116, "parentId": 115, "tags": {}, "startTime": 1751513357256, "traceId": "8ccd8366d4b03a68"}, {"name": "css-loader", "duration": 35723, "timestamp": 83208238849, "id": 118, "parentId": 115, "tags": {"astUsed": "true"}, "startTime": 1751513357735, "traceId": "8ccd8366d4b03a68"}, {"name": "build-module-tsx", "duration": 102012, "timestamp": 83208306464, "id": 119, "parentId": 103, "tags": {"name": "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\PerformanceOptimizer.tsx", "layer": "app-pages-browser"}, "startTime": 1751513357803, "traceId": "8ccd8366d4b03a68"}, {"name": "build-module-tsx", "duration": 103583, "timestamp": 83208312935, "id": 120, "parentId": 103, "tags": {"name": "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\components\\3d\\AssetLoader.tsx", "layer": "app-pages-browser"}, "startTime": 1751513357809, "traceId": "8ccd8366d4b03a68"}, {"name": "build-module-css", "duration": 799900, "timestamp": 83207735064, "id": 115, "parentId": 114, "tags": {"name": "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\globals.css", "layer": null}, "startTime": 1751513357232, "traceId": "8ccd8366d4b03a68"}, {"name": "build-module-css", "duration": 945093, "timestamp": 83207715576, "id": 114, "parentId": 103, "tags": {"name": "C:\\LocalDiskD\\UI\\AUGMENT-AI\\PORTFOLIO6\\merodev-clone\\src\\app\\globals.css", "layer": "app-pages-browser"}, "startTime": 1751513357212, "traceId": "8ccd8366d4b03a68"}, {"name": "build-module", "duration": 183, "timestamp": 83208668629, "id": 121, "parentId": 114, "tags": {}, "startTime": 1751513358165, "traceId": "8ccd8366d4b03a68"}, {"name": "add-entry", "duration": 1285191, "timestamp": 83207395547, "id": 112, "parentId": 104, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CLocalDiskD%5C%5CUI%5C%5CAUGMENT-AI%5C%5CPORTFOLIO6%5C%5Cmerodev-clone%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751513356892, "traceId": "8ccd8366d4b03a68"}]