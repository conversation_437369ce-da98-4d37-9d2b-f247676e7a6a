'use client';

import { Suspense, useRef } from 'react';
// import { useGLTF } from '@react-three/drei'; // Will be used when we have actual GLTF models
import { useFrame } from '@react-three/fiber';
import { ASSET_PATHS, BUILDING_POSITIONS } from '@/utils/constants';
import * as THREE from 'three';

// Preload assets for better performance
// useGLTF.preload(ASSET_PATHS.models.aboutBuilding);
// useGLTF.preload(ASSET_PATHS.models.projectsBuilding);
// useGLTF.preload(ASSET_PATHS.models.contactBuilding);

interface BuildingModelProps {
  modelPath: string;
  position: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  color?: string;
  emissive?: string;
  fallbackGeometry?: 'box' | 'cylinder' | 'sphere';
  fallbackSize?: [number, number, number];
  animate?: boolean;
}

function BuildingModel({
  position,
  rotation = [0, 0, 0],
  scale = [1, 1, 1],
  color = '#ffffff',
  emissive = '#000000',
  fallbackGeometry = 'box',
  fallbackSize = [2, 2, 2],
  animate = false
}: Omit<BuildingModelProps, 'modelPath'>) {
  const meshRef = useRef<THREE.Mesh>(null);

  // Add subtle animation
  useFrame((state) => {
    if (meshRef.current && animate) {
      meshRef.current.rotation.y = rotation[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.8) * 0.05;
    }
  });

  // For now, we'll use fallback geometry since we don't have actual models
  // In the future, this would load the GLTF model:
  // const { scene } = useGLTF(modelPath);

  const renderFallbackGeometry = () => {
    switch (fallbackGeometry) {
      case 'cylinder':
        return <cylinderGeometry args={fallbackSize} />;
      case 'sphere':
        return <sphereGeometry args={[fallbackSize[0]]} />;
      default:
        return <boxGeometry args={fallbackSize} />;
    }
  };

  return (
    <mesh
      ref={meshRef}
      position={position}
      rotation={rotation}
      scale={scale}
      castShadow
    >
      {renderFallbackGeometry()}
      <meshStandardMaterial
        color={color}
        emissive={emissive}
        roughness={0.7}
        metalness={0.3}
      />
    </mesh>
  );
}

// Loading fallback component
function LoadingFallback() {
  return (
    <mesh>
      <boxGeometry args={[0.5, 0.5, 0.5]} />
      <meshBasicMaterial color="#333333" wireframe />
    </mesh>
  );
}

// Error boundary for failed asset loading (will be used with actual GLTF loading)
// function AssetErrorFallback({ error, position }: { error: Error; position: [number, number, number] }) {
//   console.error('Asset loading error:', error);
//
//   return (
//     <mesh position={position}>
//       <boxGeometry args={[1, 1, 1]} />
//       <meshBasicMaterial color="#ff0000" />
//     </mesh>
//   );
// }

// Main asset loader component
interface AssetLoaderProps {
  children: React.ReactNode;
}

export function AssetLoader({ children }: AssetLoaderProps) {
  return (
    <Suspense fallback={<LoadingFallback />}>
      {children}
    </Suspense>
  );
}

// Specific building components using enhanced positioning
export function AboutBuilding() {
  const config = BUILDING_POSITIONS.about;
  return (
    <BuildingModel
      position={[config.x, config.y, config.z]}
      rotation={[config.rotation.x, config.rotation.y, config.rotation.z]}
      scale={[config.scale.x, config.scale.y, config.scale.z]}
      color="#00ffff"
      emissive="#001a1a"
      fallbackGeometry="box"
      fallbackSize={[1.5, 2, 1.5]}
      animate={true}
    />
  );
}

export function ProjectsBuilding() {
  const config = BUILDING_POSITIONS.projects;
  return (
    <BuildingModel
      modelPath={ASSET_PATHS.models.projectsBuilding}
      position={[config.x, config.y, config.z]}
      rotation={[config.rotation.x, config.rotation.y, config.rotation.z]}
      scale={[config.scale.x, config.scale.y, config.scale.z]}
      color="#ff00ff"
      emissive="#1a001a"
      fallbackGeometry="box"
      fallbackSize={[2, 3, 2]}
      animate={true}
    />
  );
}

export function ContactBuilding() {
  const config = BUILDING_POSITIONS.contact;
  return (
    <BuildingModel
      modelPath={ASSET_PATHS.models.contactBuilding}
      position={[config.x, config.y, config.z]}
      rotation={[config.rotation.x, config.rotation.y, config.rotation.z]}
      scale={[config.scale.x, config.scale.y, config.scale.z]}
      color="#ffff00"
      emissive="#1a1a00"
      fallbackGeometry="box"
      fallbackSize={[1.5, 2, 1.5]}
      animate={true}
    />
  );
}

export function DecorativeElements() {
  const decorative1 = BUILDING_POSITIONS.decorative1;
  const decorative2 = BUILDING_POSITIONS.decorative2;

  return (
    <>
      <BuildingModel
        modelPath={ASSET_PATHS.models.decorative}
        position={[decorative1.x, decorative1.y, decorative1.z]}
        rotation={[decorative1.rotation.x, decorative1.rotation.y, decorative1.rotation.z]}
        scale={[decorative1.scale.x, decorative1.scale.y, decorative1.scale.z]}
        color="#ffffff"
        emissive="#000000"
        fallbackGeometry="cylinder"
        fallbackSize={[0.3, 1, 0.3]}
        animate={false}
      />
      <BuildingModel
        modelPath={ASSET_PATHS.models.decorative}
        position={[decorative2.x, decorative2.y, decorative2.z]}
        rotation={[decorative2.rotation.x, decorative2.rotation.y, decorative2.rotation.z]}
        scale={[decorative2.scale.x, decorative2.scale.y, decorative2.scale.z]}
        color="#ffffff"
        emissive="#000000"
        fallbackGeometry="cylinder"
        fallbackSize={[0.3, 1, 0.3]}
        animate={false}
      />
    </>
  );
}

export function BackgroundElements() {
  return (
    <>
      {Object.entries(BUILDING_POSITIONS)
        .filter(([key]) => key.startsWith('background'))
        .map(([key, config]) => (
          <BuildingModel
            key={key}
            modelPath={ASSET_PATHS.models.decorative}
            position={[config.x, config.y, config.z]}
            rotation={[config.rotation.x, config.rotation.y, config.rotation.z]}
            scale={[config.scale.x, config.scale.y, config.scale.z]}
            color="#0a0a0a"
            emissive="#000000"
            fallbackGeometry="box"
            fallbackSize={[1, 1, 1]}
            animate={false}
          />
        ))}
    </>
  );
}

// Hook for asset loading progress (future implementation)
export function useAssetLoadingProgress() {
  // This would track loading progress of all assets
  // For now, return mock data
  return {
    progress: 100,
    isLoading: false,
    error: null,
    loadedAssets: ['placeholder'],
  };
}
