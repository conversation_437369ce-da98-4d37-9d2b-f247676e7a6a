'use client';

import { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { LIGHT_CONFIG } from '@/utils/constants';
import * as THREE from 'three';

export function Lighting() {
  const spotLight1Ref = useRef<THREE.SpotLight>(null);
  const spotLight2Ref = useRef<THREE.SpotLight>(null);

  // Animate spot lights
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    
    if (spotLight1Ref.current) {
      spotLight1Ref.current.intensity = LIGHT_CONFIG.spot[0].intensity * (0.8 + 0.2 * Math.sin(time * 2));
    }
    
    if (spotLight2Ref.current) {
      spotLight2Ref.current.intensity = LIGHT_CONFIG.spot[1].intensity * (0.8 + 0.2 * Math.sin(time * 1.5 + Math.PI));
    }
  });

  return (
    <>
      {/* Ambient light for overall illumination */}
      <ambientLight 
        color={LIGHT_CONFIG.ambient.color} 
        intensity={LIGHT_CONFIG.ambient.intensity} 
      />
      
      {/* Main directional light (sun-like) */}
      <directionalLight
        color={LIGHT_CONFIG.directional.color}
        intensity={LIGHT_CONFIG.directional.intensity}
        position={[
          LIGHT_CONFIG.directional.position.x,
          LIGHT_CONFIG.directional.position.y,
          LIGHT_CONFIG.directional.position.z
        ]}
        castShadow={LIGHT_CONFIG.directional.castShadow}
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      
      {/* Accent spot lights for atmosphere */}
      <spotLight
        ref={spotLight1Ref}
        color={LIGHT_CONFIG.spot[0].color}
        intensity={LIGHT_CONFIG.spot[0].intensity}
        position={[
          LIGHT_CONFIG.spot[0].position.x,
          LIGHT_CONFIG.spot[0].position.y,
          LIGHT_CONFIG.spot[0].position.z
        ]}
        target-position={[
          LIGHT_CONFIG.spot[0].target.x,
          LIGHT_CONFIG.spot[0].target.y,
          LIGHT_CONFIG.spot[0].target.z
        ]}
        angle={LIGHT_CONFIG.spot[0].angle}
        penumbra={LIGHT_CONFIG.spot[0].penumbra}
        decay={2}
        distance={30}
      />
      
      <spotLight
        ref={spotLight2Ref}
        color={LIGHT_CONFIG.spot[1].color}
        intensity={LIGHT_CONFIG.spot[1].intensity}
        position={[
          LIGHT_CONFIG.spot[1].position.x,
          LIGHT_CONFIG.spot[1].position.y,
          LIGHT_CONFIG.spot[1].position.z
        ]}
        target-position={[
          LIGHT_CONFIG.spot[1].target.x,
          LIGHT_CONFIG.spot[1].target.y,
          LIGHT_CONFIG.spot[1].target.z
        ]}
        angle={LIGHT_CONFIG.spot[1].angle}
        penumbra={LIGHT_CONFIG.spot[1].penumbra}
        decay={2}
        distance={25}
      />
    </>
  );
}
