"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/3d/AssetLoader.tsx":
/*!*******************************************!*\
  !*** ./src/components/3d/AssetLoader.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutBuilding: () => (/* binding */ AboutBuilding),\n/* harmony export */   AssetLoader: () => (/* binding */ AssetLoader),\n/* harmony export */   BackgroundElements: () => (/* binding */ BackgroundElements),\n/* harmony export */   ContactBuilding: () => (/* binding */ ContactBuilding),\n/* harmony export */   DecorativeElements: () => (/* binding */ DecorativeElements),\n/* harmony export */   ProjectsBuilding: () => (/* binding */ ProjectsBuilding),\n/* harmony export */   useAssetLoadingProgress: () => (/* binding */ useAssetLoadingProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/@react-three/fiber/dist/events-f681e724.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/constants */ \"(app-pages-browser)/./src/utils/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ AssetLoader,AboutBuilding,ProjectsBuilding,ContactBuilding,DecorativeElements,BackgroundElements,useAssetLoadingProgress auto */ \nvar _s = $RefreshSig$();\n\n// import { useGLTF } from '@react-three/drei'; // Will be used when we have actual GLTF models\n\n\nfunction BuildingModel(param) {\n    let { position, rotation = [\n        0,\n        0,\n        0\n    ], scale = [\n        1,\n        1,\n        1\n    ], color = '#ffffff', emissive = '#000000', fallbackGeometry = 'box', fallbackSize = [\n        2,\n        2,\n        2\n    ], animate = false } = param;\n    _s();\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Add subtle animation\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C)({\n        \"BuildingModel.useFrame\": (state)=>{\n            if (meshRef.current && animate) {\n                meshRef.current.rotation.y = rotation[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n                meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.8) * 0.05;\n            }\n        }\n    }[\"BuildingModel.useFrame\"]);\n    // For now, we'll use fallback geometry since we don't have actual models\n    // In the future, this would load the GLTF model:\n    // const { scene } = useGLTF(modelPath);\n    const renderFallbackGeometry = ()=>{\n        switch(fallbackGeometry){\n            case 'cylinder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cylinderGeometry\", {\n                    args: fallbackSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n            case 'sphere':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"sphereGeometry\", {\n                    args: [\n                        fallbackSize[0]\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                    args: fallbackSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        ref: meshRef,\n        position: position,\n        rotation: rotation,\n        scale: scale,\n        castShadow: true,\n        children: [\n            renderFallbackGeometry(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                color: color,\n                emissive: emissive,\n                roughness: 0.7,\n                metalness: 0.3\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(BuildingModel, \"/vg1AmA8+P3+Fj0/y210JTVKtL0=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C\n    ];\n});\n_c = BuildingModel;\n// Loading fallback component\nfunction LoadingFallback() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                args: [\n                    0.5,\n                    0.5,\n                    0.5\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshBasicMaterial\", {\n                color: \"#333333\",\n                wireframe: true\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoadingFallback;\nfunction AssetLoader(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n            fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n            lineNumber: 109,\n            columnNumber: 25\n        }, void 0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_c2 = AssetLoader;\n// Specific building components using enhanced positioning\nfunction AboutBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.about;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.aboutBuilding,\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#00ffff\",\n        emissive: \"#001a1a\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            1.5,\n            2,\n            1.5\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c3 = AboutBuilding;\nfunction ProjectsBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.projects;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.projectsBuilding,\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#ff00ff\",\n        emissive: \"#1a001a\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            2,\n            3,\n            2\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_c4 = ProjectsBuilding;\nfunction ContactBuilding() {\n    const config = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.contact;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n        modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.contactBuilding,\n        position: [\n            config.x,\n            config.y,\n            config.z\n        ],\n        rotation: [\n            config.rotation.x,\n            config.rotation.y,\n            config.rotation.z\n        ],\n        scale: [\n            config.scale.x,\n            config.scale.y,\n            config.scale.z\n        ],\n        color: \"#ffff00\",\n        emissive: \"#1a1a00\",\n        fallbackGeometry: \"box\",\n        fallbackSize: [\n            1.5,\n            2,\n            1.5\n        ],\n        animate: true\n    }, void 0, false, {\n        fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_c5 = ContactBuilding;\nfunction DecorativeElements() {\n    const decorative1 = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.decorative1;\n    const decorative2 = _utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS.decorative2;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    decorative1.x,\n                    decorative1.y,\n                    decorative1.z\n                ],\n                rotation: [\n                    decorative1.rotation.x,\n                    decorative1.rotation.y,\n                    decorative1.rotation.z\n                ],\n                scale: [\n                    decorative1.scale.x,\n                    decorative1.scale.y,\n                    decorative1.scale.z\n                ],\n                color: \"#ffffff\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"cylinder\",\n                fallbackSize: [\n                    0.3,\n                    1,\n                    0.3\n                ],\n                animate: false\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    decorative2.x,\n                    decorative2.y,\n                    decorative2.z\n                ],\n                rotation: [\n                    decorative2.rotation.x,\n                    decorative2.rotation.y,\n                    decorative2.rotation.z\n                ],\n                scale: [\n                    decorative2.scale.x,\n                    decorative2.scale.y,\n                    decorative2.scale.z\n                ],\n                color: \"#ffffff\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"cylinder\",\n                fallbackSize: [\n                    0.3,\n                    1,\n                    0.3\n                ],\n                animate: false\n            }, void 0, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c6 = DecorativeElements;\nfunction BackgroundElements() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: Object.entries(_utils_constants__WEBPACK_IMPORTED_MODULE_2__.BUILDING_POSITIONS).filter((param)=>{\n            let [key] = param;\n            return key.startsWith('background');\n        }).map((param)=>{\n            let [key, config] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingModel, {\n                modelPath: _utils_constants__WEBPACK_IMPORTED_MODULE_2__.ASSET_PATHS.models.decorative,\n                position: [\n                    config.x,\n                    config.y,\n                    config.z\n                ],\n                rotation: [\n                    config.rotation.x,\n                    config.rotation.y,\n                    config.rotation.z\n                ],\n                scale: [\n                    config.scale.x,\n                    config.scale.y,\n                    config.scale.z\n                ],\n                color: \"#0a0a0a\",\n                emissive: \"#000000\",\n                fallbackGeometry: \"box\",\n                fallbackSize: [\n                    1,\n                    1,\n                    1\n                ],\n                animate: false\n            }, key, false, {\n                fileName: \"C:\\\\LocalDiskD\\\\UI\\\\AUGMENT-AI\\\\PORTFOLIO6\\\\merodev-clone\\\\src\\\\components\\\\3d\\\\AssetLoader.tsx\",\n                lineNumber: 205,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false);\n}\n_c7 = BackgroundElements;\n// Hook for asset loading progress (future implementation)\nfunction useAssetLoadingProgress() {\n    // This would track loading progress of all assets\n    // For now, return mock data\n    return {\n        progress: 100,\n        isLoading: false,\n        error: null,\n        loadedAssets: [\n            'placeholder'\n        ]\n    };\n}\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"BuildingModel\");\n$RefreshReg$(_c1, \"LoadingFallback\");\n$RefreshReg$(_c2, \"AssetLoader\");\n$RefreshReg$(_c3, \"AboutBuilding\");\n$RefreshReg$(_c4, \"ProjectsBuilding\");\n$RefreshReg$(_c5, \"ContactBuilding\");\n$RefreshReg$(_c6, \"DecorativeElements\");\n$RefreshReg$(_c7, \"BackgroundElements\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/3d/AssetLoader.tsx\n"));

/***/ })

});